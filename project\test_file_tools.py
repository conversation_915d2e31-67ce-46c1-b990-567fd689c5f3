#!/usr/bin/env python3
"""
Test script for the File Tools (PCAP and Log file operations).

This script demonstrates and tests the file system tools that work with
PCAP files and log files in local directories.
"""

import os
import json
from datetime import datetime
from file_tools import (
    list_pcap_files, list_log_files, get_pcap_file_info, 
    get_log_file_content, search_log_files, create_file_config, 
    make_file_tools, FileSystemError
)
from config import FileConfig

def create_sample_files():
    """Create sample PCAP and log files for testing"""
    print("📁 Creating sample files for testing...")
    
    # Create directories if they don't exist
    os.makedirs(FileConfig.PCAP_FOLDER, exist_ok=True)
    os.makedirs(FileConfig.LOG_FOLDER, exist_ok=True)
    
    # Create sample log files
    sample_logs = [
        ("app.log", [
            "2024-01-15 10:30:00 INFO Application started",
            "2024-01-15 10:30:01 INFO Database connection established",
            "2024-01-15 10:30:05 WARNING High memory usage detected",
            "2024-01-15 10:30:10 ERROR Failed to process request",
            "2024-01-15 10:30:15 INFO Request processed successfully",
            "2024-01-15 10:30:20 DEBUG User authentication completed",
            "2024-01-15 10:30:25 ERROR Database connection lost",
            "2024-01-15 10:30:30 INFO Attempting to reconnect to database",
            "2024-01-15 10:30:35 INFO Database connection restored",
            "2024-01-15 10:30:40 INFO Application running normally"
        ]),
        ("test.log", [
            "2024-01-15 11:00:00 INFO Test suite started",
            "2024-01-15 11:00:05 INFO Running test case 1",
            "2024-01-15 11:00:10 PASS Test case 1 completed",
            "2024-01-15 11:00:15 INFO Running test case 2", 
            "2024-01-15 11:00:20 FAIL Test case 2 failed - assertion error",
            "2024-01-15 11:00:25 INFO Running test case 3",
            "2024-01-15 11:00:30 PASS Test case 3 completed",
            "2024-01-15 11:00:35 INFO Test suite completed",
            "2024-01-15 11:00:40 INFO 2 passed, 1 failed"
        ]),
        ("error.log", [
            "2024-01-15 12:00:00 ERROR Critical system failure",
            "2024-01-15 12:00:05 ERROR Stack trace: NullPointerException",
            "2024-01-15 12:00:10 ERROR Unable to recover from error",
            "2024-01-15 12:00:15 ERROR System shutdown initiated"
        ])
    ]
    
    for filename, lines in sample_logs:
        log_path = os.path.join(FileConfig.LOG_FOLDER, filename)
        with open(log_path, 'w') as f:
            f.write('\n'.join(lines) + '\n')
        print(f"✅ Created {log_path}")
    
    # Create sample PCAP info files (we can't create real PCAP files easily)
    sample_pcap_info = [
        "capture1.pcap",
        "network_traffic.pcap", 
        "test_capture.pcapng"
    ]
    
    for filename in sample_pcap_info:
        pcap_path = os.path.join(FileConfig.PCAP_FOLDER, filename)
        # Create empty files to simulate PCAP files
        with open(pcap_path, 'wb') as f:
            f.write(b'\x00' * 1024)  # Write 1KB of null bytes
        print(f"✅ Created {pcap_path} (sample)")
    
    print("📁 Sample files created successfully!")

def test_file_listing():
    """Test file listing functions"""
    print("\n📋 Testing File Listing Functions")
    print("-" * 40)
    
    try:
        # Test PCAP file listing
        print("1. Testing PCAP file listing...")
        pcap_files = list_pcap_files(FileConfig.PCAP_FOLDER)
        print(f"✅ Found {len(pcap_files)} PCAP files")
        for file_info in pcap_files[:3]:  # Show first 3
            print(f"   - {file_info['filename']} ({file_info['size_mb']} MB)")
        
        # Test log file listing
        print("\n2. Testing log file listing...")
        log_files = list_log_files(FileConfig.LOG_FOLDER)
        print(f"✅ Found {len(log_files)} log files")
        for file_info in log_files[:3]:  # Show first 3
            print(f"   - {file_info['filename']} ({file_info['size_kb']} KB)")
        
        return True
        
    except FileSystemError as e:
        print(f"❌ File listing test failed: {e}")
        return False

def test_file_info():
    """Test file information functions"""
    print("\n📊 Testing File Information Functions")
    print("-" * 40)
    
    try:
        # Test PCAP file info
        pcap_files = list_pcap_files(FileConfig.PCAP_FOLDER)
        if pcap_files:
            filename = pcap_files[0]['filename']
            print(f"1. Getting info for PCAP file: {filename}")
            info = get_pcap_file_info(filename, FileConfig.PCAP_FOLDER)
            print(f"✅ File size: {info['size_bytes']} bytes")
            print(f"   Modified: {info['modified_time']}")
            print(f"   Readable: {info['is_readable']}")
        else:
            print("⚠️  No PCAP files found for testing")
        
        return True
        
    except FileSystemError as e:
        print(f"❌ File info test failed: {e}")
        return False

def test_log_content():
    """Test log file content reading"""
    print("\n📖 Testing Log Content Reading")
    print("-" * 40)
    
    try:
        log_files = list_log_files(FileConfig.LOG_FOLDER)
        if log_files:
            filename = log_files[0]['filename']
            print(f"1. Reading content from: {filename}")
            
            # Test reading with default settings
            content = get_log_file_content(filename, FileConfig.LOG_FOLDER, max_lines=5, tail=True)
            print(f"✅ Read {content['returned_lines']} lines (last 5)")
            print(f"   Total lines in file: {content['total_lines']}")
            print(f"   Truncated: {content['truncated']}")
            
            # Show a sample of content
            lines = content['content'].split('\n')[:3]
            for line in lines:
                if line.strip():
                    print(f"   > {line}")
        else:
            print("⚠️  No log files found for testing")
        
        return True
        
    except FileSystemError as e:
        print(f"❌ Log content test failed: {e}")
        return False

def test_log_search():
    """Test log file searching"""
    print("\n🔍 Testing Log File Search")
    print("-" * 40)
    
    try:
        # Search for common terms
        search_terms = ["ERROR", "INFO", "test"]
        
        for term in search_terms:
            print(f"Searching for '{term}'...")
            results = search_log_files(term, FileConfig.LOG_FOLDER, max_results=5)
            print(f"✅ Found {len(results)} matches")
            
            # Show first few results
            for result in results[:2]:
                print(f"   {result['filename']}:{result['line_number']} - {result['line_content'][:50]}...")
        
        return True
        
    except FileSystemError as e:
        print(f"❌ Log search test failed: {e}")
        return False

def test_tool_wrappers():
    """Test the LangChain tool wrappers"""
    print("\n🛠️  Testing Tool Wrappers")
    print("-" * 40)
    
    try:
        # Create file tools
        file_config = create_file_config(FileConfig.PCAP_FOLDER, FileConfig.LOG_FOLDER)
        (list_pcap_func, list_log_func, get_pcap_info_func, 
         get_log_content_func, search_logs_func) = make_file_tools(file_config)
        
        # Test each tool wrapper
        print("1. Testing list_pcap_files tool...")
        result = list_pcap_func("*.pcap")
        pcap_data = json.loads(result)
        print(f"✅ Tool returned {len(pcap_data)} PCAP files")
        
        print("\n2. Testing list_log_files tool...")
        result = list_log_func("*.log")
        log_data = json.loads(result)
        print(f"✅ Tool returned {len(log_data)} log files")
        
        print("\n3. Testing search_logs tool...")
        result = search_logs_func("ERROR", 3)
        search_data = json.loads(result)
        print(f"✅ Tool found {len(search_data)} matches for 'ERROR'")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool wrapper test failed: {e}")
        return False

def test_agent_integration():
    """Test integration with the unified agent"""
    print("\n🤖 Testing Agent Integration")
    print("-" * 40)
    
    try:
        from agent import UnifiedReActAgent
        from config import GitLabConfig, MistralConfig, AgentConfig
        
        # Create agent with file tools enabled
        agent = UnifiedReActAgent(
            gitlab_config=GitLabConfig.to_dict(),
            mistral_api_key=MistralConfig.API_KEY,
            mistral_model=MistralConfig.MODEL,
            enable_sql=False  # Disable SQL for this test
        )
        
        print("✅ Agent created with file tools")
        
        # Test a simple file query
        print("Testing file query...")
        response = agent.process_query("List all log files in the log folder")
        print(f"✅ Agent responded: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent integration test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 File Tools Test Suite")
    print("=" * 50)
    
    # Create sample files first
    create_sample_files()
    
    # Run tests
    tests = [
        ("File Listing", test_file_listing),
        ("File Information", test_file_info),
        ("Log Content Reading", test_log_content),
        ("Log Search", test_log_search),
        ("Tool Wrappers", test_tool_wrappers),
        ("Agent Integration", test_agent_integration)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if i < passed else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    total = len(tests)
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All file tools tests passed!")
        print("\nYou can now use file tools in your agent:")
        print("- List PCAP files: 'List all PCAP files'")
        print("- Read logs: 'Show me the content of app.log'")
        print("- Search logs: 'Search for ERROR in all log files'")
    else:
        print("⚠️  Some tests failed. Check the file system setup.")

if __name__ == "__main__":
    main()
