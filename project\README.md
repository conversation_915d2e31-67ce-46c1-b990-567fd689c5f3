# Unified GitLab & SQL ReAct Agent

A powerful React agent that combines GitLab repository operations with SQL Server database queries using LangChain and Mistral AI.

## Features

### 🔧 GitLab Operations
- **File Content Retrieval**: Get file content from any branch or commit
- **SHA Hash Lookup**: Retrieve file SHA hashes for version tracking
- **Historical Queries**: Access file content from specific timestamps
- **Branch Operations**: Work with different branches and commit references

### 🗄️ SQL Database Operations
- **Test Execution Queries**: Query test execution data
- **Testcase Management**: Retrieve testcase information
- **Run Analysis**: Analyze test run results and performance
- **Dynamic SQL Generation**: AI-powered SQL query construction

### 🤖 Intelligent Agent Features
- **Natural Language Processing**: Ask questions in plain English
- **Multi-step Reasoning**: Handles complex queries requiring multiple operations
- **Error Handling**: Robust error handling and user feedback
- **Interactive Mode**: Real-time query processing

## Installation

1. **Clone or download the repository**

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Set up environment variables** (optional):
```bash
# Create a .env file
MISTRAL_API_KEY=your_mistral_api_key_here
```

## Configuration

### GitLab Setup
Update the GitLab configuration in your script:

```python
gitlab_config = create_gitlab_config(
    gitlab_url="https://gitlab.com",  # Your GitLab instance
    project_id="your_project_id",     # Your project ID
    token="your_gitlab_token"         # Your GitLab access token
)
```

### SQL Server Setup
The agent automatically connects to SQL Server using:
- **Server**: localhost
- **Database**: STAGE
- **Authentication**: Windows Authentication (Trusted_Connection)
- **Tables**: execution, testcase, run

To modify the database connection, update the `create_database_connection()` function in `agent.py`.

## Usage

### Basic Usage

```python
from agent import UnifiedReActAgent, create_gitlab_config

# Configure GitLab
gitlab_config = create_gitlab_config(
    gitlab_url="https://gitlab.com",
    project_id="71217006",
    token="**************************"
)

# Create agent
agent = UnifiedReActAgent(
    gitlab_config=gitlab_config,
    mistral_api_key="your_api_key",
    enable_sql=True
)

# Ask questions
response = agent.process_query("Show me the content of README.md from main branch")
print(response)
```

### Running the Example

```bash
python unified_agent_example.py
```

### Interactive Mode

```bash
python agent.py
```

## Example Queries

### GitLab Queries
```
Show me the content of README.md from main branch
What's the SHA of .gitlab-ci.yml on main branch?
Get the content of config.py from main branch as it was on 2024-01-15T10:30:00Z
Display utils.py from develop branch
```

### SQL Queries
```
Show all executions for testcase ID 1
What are the most recent commit references for testcase ID 3?
Retrieve result, duration, and timestamp for execution of testcase ID 2 in run ID 2
List all testcases with their latest execution status
```

### Mixed Queries
The agent can intelligently determine whether your query is about GitLab or SQL and route it accordingly.

## Architecture

### Components

1. **UnifiedReActAgent**: Main agent class that orchestrates both GitLab and SQL operations
2. **MistralLLM**: LLM wrapper for GitLab operations
3. **CustomMistralLLM**: Chat-based LLM wrapper for SQL operations
4. **GitLab Tools**: File operations, SHA retrieval, content fetching
5. **SQL Tools**: Database schema inspection, query execution, result formatting

### Tool Categories

#### GitLab Tools
- `get_file_sha`: Get SHA hash of a file at specific commit
- `get_file_sha_from_branch_timestamp`: Get SHA from branch at timestamp
- `get_file_content`: Retrieve file content using SHA
- `extract_info`: Parse user queries for GitLab operations

#### SQL Tools
- `sql_db_schema`: Inspect database schema
- `sql_db_query_checker`: Validate SQL queries
- `sql_db_query`: Execute SQL queries
- `final_answer_tool`: Format SQL results

## Configuration Options

### Agent Configuration
```python
agent = UnifiedReActAgent(
    gitlab_config=gitlab_config,
    mistral_api_key="your_key",
    mistral_model="mistral-large-latest",  # Model selection
    enable_sql=True  # Enable/disable SQL functionality
)
```

### Database Tables
The agent is configured to work with these tables:
- `execution`: Test execution records
- `testcase`: Test case definitions  
- `run`: Test run information

Modify `Config.DB_TABLES` to include additional tables.

## Error Handling

The agent includes comprehensive error handling:
- **GitLab API Errors**: Network issues, authentication, file not found
- **SQL Errors**: Connection issues, invalid queries, permission errors
- **LLM Errors**: API rate limits, invalid responses
- **Parsing Errors**: Malformed inputs, unexpected formats

## Troubleshooting

### Common Issues

1. **GitLab Authentication Error**
   - Verify your GitLab token has appropriate permissions
   - Check project ID is correct

2. **SQL Connection Error**
   - Ensure SQL Server is running
   - Verify database name and connection string
   - Check Windows Authentication is enabled

3. **Mistral API Error**
   - Verify API key is valid
   - Check rate limits and quotas

### Debug Mode

Enable verbose logging by setting `verbose=True` in the AgentExecutor configuration.

## Dependencies

- `langchain`: LLM framework and agent orchestration
- `langchain-community`: Community tools and utilities
- `mistralai`: Mistral AI API client
- `sqlalchemy`: Database ORM and connection management
- `pyodbc`: SQL Server database driver
- `requests`: HTTP client for GitLab API
- `python-dotenv`: Environment variable management

## License

This project is provided as-is for educational and development purposes.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the agent's capabilities.
