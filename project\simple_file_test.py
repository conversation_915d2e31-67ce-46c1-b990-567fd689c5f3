#!/usr/bin/env python3
"""
Simple test to verify file tools work with a basic agent setup.
"""

from langchain.agents import create_react_agent, AgentExecutor
from langchain_core.prompts import PromptTemplate
from langchain_core.tools import Tool
from agent import <PERSON>stralLL<PERSON>
from file_tools import make_file_tools, create_file_config
from config import FileConfig, MistralConfig

def create_simple_file_agent():
    """Create a simple agent with just file tools"""
    
    # Create LLM
    llm = MistralLLM(api_key=MistralConfig.API_KEY, model=MistralConfig.MODEL)
    
    # Create file tools
    file_config = create_file_config(FileConfig.PCAP_FOLDER, FileConfig.LOG_FOLDER)
    (list_pcap_func, list_log_func, get_pcap_info_func, 
     get_log_content_func, search_logs_func) = make_file_tools(file_config)
    
    # Create simple tool wrappers
    def simple_list_pcap_wrapper(input_str: str) -> str:
        """Simple wrapper for listing PCAP files"""
        import os
        print(f"[DEBUG] Tool called with input: {repr(input_str)}")
        print(f"[DEBUG] Current working directory: {os.getcwd()}")
        print(f"[DEBUG] PCAP folder: {FileConfig.PCAP_FOLDER}")
        print(f"[DEBUG] PCAP folder exists: {os.path.exists(FileConfig.PCAP_FOLDER)}")

        try:
            # Remove quotes and whitespace
            pattern = input_str.strip().strip("'\"") if input_str.strip() else "*.pcap"
            print(f"[DEBUG] Using pattern: {pattern}")

            # Test direct call first
            from file_tools import list_pcap_files
            direct_result = list_pcap_files(FileConfig.PCAP_FOLDER, pattern)
            print(f"[DEBUG] Direct call result: {len(direct_result)} files")

            # Now test tool function
            result = list_pcap_func(pattern)
            print(f"[DEBUG] Tool function result type: {type(result)}")
            print(f"[DEBUG] Tool function result length: {len(result)}")
            print(f"[DEBUG] Tool function result preview: {result[:100]}...")

            return result
        except Exception as e:
            error_msg = f"Error listing PCAP files: {str(e)}"
            print(f"[DEBUG] Tool error: {error_msg}")
            import traceback
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
            return error_msg
    
    # Create tools
    tools = [
        Tool(
            name="list_pcap_files",
            description="List PCAP files in the configured folder. Input should be a file pattern like '*.pcap'.",
            func=simple_list_pcap_wrapper
        )
    ]
    
    # Create simple prompt
    prompt = PromptTemplate.from_template("""
You are a file system assistant. You can list PCAP files.

You have access to these tools:
{tools}

Use this format:
Question: the input question
Thought: think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
Thought: I now know the final answer
Final Answer: the final answer

Question: {input}
Thought: {agent_scratchpad}
""")
    
    # Create agent
    agent = create_react_agent(llm, tools, prompt)
    
    # Create executor
    agent_executor = AgentExecutor(
        agent=agent,
        tools=tools,
        verbose=True,
        handle_parsing_errors=True,
        max_iterations=5
    )
    
    return agent_executor

def test_simple_agent():
    """Test the simple agent"""
    print("🧪 Testing Simple File Agent")
    print("=" * 50)
    
    try:
        # Create agent
        agent = create_simple_file_agent()
        
        # Test query
        print("Testing query: 'List all PCAP files'")
        response = agent.invoke({"input": "List all PCAP files"})
        print(f"Response: {response['output']}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_agent()
