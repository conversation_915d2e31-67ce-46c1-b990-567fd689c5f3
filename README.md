
# AI Agents Framework

A comprehensive, modular framework for building and deploying AI agents using LangGraph. This framework provides a flexible architecture for creating intelligent agents with workflow-based execution, pluggable memory systems, and extensible tool integration.

## 🌟 Features

- **🤖 Modular Agent Architecture**: Build agents with clear interfaces and reusable components
- **� Graph-Based Workflows**: Define complex agent behaviors using YAML-configured workflows
- **�🔧 Extensible Tool System**: Integrate custom tools and external APIs seamlessly
- **💾 Pluggable Memory Backends**: Support for Redis and vector store memory systems
- **🚀 Multiple Deployment Options**: CLI, API, and containerized deployment support
- **🔍 Built-in Agent Types**: Sales assistant, research agent, and supervisor agents
- **📈 Workflow Visualization**: Generate visual graphs of your agent workflows
- **🧪 Testing Framework**: Comprehensive testing utilities for agent development
- **⚙️ Flexible Configuration**: YAML-based configuration with environment variable support

## 🚀 Quick Start

### Prerequisites

- Python 3.9+ (excluding 3.9.7)
- Poetry for dependency management
- OpenAI API key (or compatible API endpoint)

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd ai-agents
pip install poetry
poetry install
```

2. **Set up environment variables:**
```bash
# Create .env file in project root
cat > .env << EOF
PYTHONPATH=src
OPENAI_API_KEY=your-openai-api-key-here
EOF
```

3. **Run your first agent:**
```bash
# Run the sales assistant agent
python -m ai_agents.execution.cli sales_assistant

# Run with custom input
python -m ai_agents.execution.cli sales_assistant --input '{"customer_query": "What products do you offer?"}'

# Run the research agent
python -m ai_agents.execution.cli research --input '{"query": "latest AI developments"}'
```

## 📁 Project Structure

```
ai-agents/
├── src/ai_agents/           # Main package
│   ├── agents/              # Agent implementations
│   │   ├── sales_assistant/ # Sales agent with database integration
│   │   ├── research_agent/  # Research agent with web search
│   │   ├── supervisor/      # Multi-agent supervisor
│   │   ├── agent_base.py    # Base agent interface
│   │   └── registry.py      # Agent registry
│   ├── memory/              # Memory backend implementations
│   │   ├── redis_memory.py  # Redis-based memory
│   │   ├── vector_store.py  # Vector store memory
│   │   └── factory.py       # Memory factory
│   ├── model/               # LLM integration
│   │   └── loader.py        # Model loading utilities
│   ├── tools/               # Tool implementations
│   │   ├── search.py        # Web search tools
│   │   ├── base.py          # Base tool interface
│   │   └── tool_registry.py # Tool registry
│   ├── workflows/           # Workflow management
│   │   ├── core/            # Core workflow handlers
│   │   ├── templates/       # Workflow templates
│   │   └── visualizer/      # Graph visualization
│   ├── execution/           # Agent execution
│   │   ├── cli.py           # Command-line interface
│   │   ├── runner.py        # Agent runner
│   │   └── batch.py         # Batch processing
│   ├── deployment/          # Deployment options
│   │   ├── api.py           # FastAPI deployment
│   │   └── Dockerfile       # Container deployment
│   └── utils/               # Utility modules
├── tests/                   # Test suite
├── docs/                    # Documentation
└── pyproject.toml          # Project configuration
```

## 🏗️ Architecture Overview

### Core Components

#### 1. **Agent Base (`AgentBase`)**
All agents inherit from the `AgentBase` abstract class, providing a consistent interface:
- `run(input_data)`: Execute the agent with input data
- `build_agent()`: Create the agent's implementation (StateGraph, AgentExecutor, etc.)
- `build_model()`: Configure the language model
- `build_memory()`: Set up memory backend
- `build_tools()`: Initialize agent tools

#### 2. **Workflow Agents (`WorkflowAgent`)**
YAML-configured agents that define behavior through workflow graphs:
- **Nodes**: Individual processing steps (entry, function, decision, exit, agent)
- **Edges**: Transitions between nodes
- **State Schema**: Define data structure passed between nodes
- **Handlers**: Python functions that implement node logic

#### 3. **Memory Systems**
Pluggable memory backends for agent persistence:
- **Redis Memory**: Key-value storage for session data
- **Vector Store Memory**: Semantic search and retrieval
- **Memory Factory**: Automatic memory backend creation

#### 4. **Tool System**
Extensible tool framework for external integrations:
- **Base Tool Interface**: Consistent tool API
- **Built-in Tools**: Web search, Wikipedia, database queries
- **Tool Registry**: Centralized tool management

### Agent Types

#### Sales Assistant Agent
- **Purpose**: Customer service and sales support
- **Features**: Database integration, customer query processing
- **Workflow**: Query processing → Database lookup → Response generation
- **Memory**: Vector store for product knowledge
- **Tools**: Database query tools

#### Research Agent
- **Purpose**: Information gathering and research
- **Features**: Web search, Wikipedia integration
- **Workflow**: LangChain-based agent executor
- **Tools**: DuckDuckGo search, Wikipedia API
- **Memory**: Configurable memory backend

#### Supervisor Agent
- **Purpose**: Multi-agent coordination and task delegation
- **Features**: Agent orchestration, response review
- **Workflow**: Task delegation → Agent execution → Review → Response
- **Memory**: Vector store for coordination history

## 🔧 Configuration

### Agent Configuration (YAML)

Each agent is configured via a YAML file defining its workflow, state schema, memory, and model settings:

```yaml
workflow:
  entrypoint: start
  error_node: error_handler
  nodes:
    - name: start
      handler: start_node
      type: entry
    - name: process
      handler: process_data
      type: function
    - name: finish
      handler: end_node
      type: exit
  edges:
    - from: start
      to: process
    - from: process
      to: finish

state_schema:
  input_query: string
  result: string
  error: string

memory:
  type: vector
  config:
    embedding_model: openai
    store_path: ./vector_store

model:
  type: openai
  model_id: gpt-4
  config:
    temperature: 0.7
    max_tokens: 1000
    openai_api_base: https://api.openai.com/v1  # Optional custom endpoint
```

### Environment Variables

```bash
# Required
OPENAI_API_KEY=your-openai-api-key-here
PYTHONPATH=src

# Optional - Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Optional - Custom OpenAI endpoint
OPENAI_API_BASE=https://your-custom-endpoint.com
```

## 💻 Usage Examples

### Command Line Interface

```bash
# Basic agent execution
python -m ai_agents.execution.cli sales_assistant

# With custom input data
python -m ai_agents.execution.cli research --input '{"query": "machine learning trends 2024"}'

# With custom configuration file
python -m ai_agents.execution.cli sales_assistant --config path/to/custom_config.yaml

# Available agents
python -m ai_agents.execution.cli --help
```

### Programmatic Usage

```python
from ai_agents.agents.registry import get_agent
from ai_agents.execution.runner import run_agent

# Get an agent instance
agent = get_agent("sales_assistant")

# Run with input data
result = run_agent(agent, {
    "customer_query": "What are your pricing options?"
})

print(result)
```

### Custom Agent Development

```python
from ai_agents.agents.workflow_agent import WorkflowAgent
from ai_agents.utils.config_loader import load_yaml_config

class MyCustomAgent(WorkflowAgent):
    default_config_path = "path/to/my_agent/config.yaml"

    @property
    def handler_module(self):
        from . import handlers
        return handlers

    @property
    def prompt_dir(self):
        return "path/to/my_agent/prompts"

    def build_tools(self):
        # Return custom tools
        return {"my_tool": MyCustomTool()}

# Register the agent
from ai_agents.agents.registry import AGENT_REGISTRY
AGENT_REGISTRY["my_agent"] = MyCustomAgent
```

## 🚀 Deployment Options

### 1. Command Line Deployment
Direct execution via CLI for development and testing:
```bash
python -m ai_agents.execution.cli <agent_name> --input '<json_input>'
```

### 2. API Deployment
Deploy agents as REST APIs using FastAPI:
```bash
# Start the API server
python -m ai_agents.deployment.api

# Make requests to your agents
curl -X POST "http://localhost:8000/agents/sales_assistant/run" \
     -H "Content-Type: application/json" \
     -d '{"customer_query": "Tell me about your products"}'
```

### 3. Container Deployment
Use Docker for scalable deployment:
```bash
# Build the container
docker build -t ai-agents -f src/ai_agents/deployment/Dockerfile .

# Run the container
docker run -p 8000:8000 -e OPENAI_API_KEY=your-key ai-agents
```

### 4. Batch Processing
Process multiple inputs in batch mode:
```python
from ai_agents.execution.batch import BatchProcessor

processor = BatchProcessor("sales_assistant")
results = processor.process_batch([
    {"customer_query": "What products do you offer?"},
    {"customer_query": "What are your prices?"},
    {"customer_query": "Do you offer support?"}
])
```

## 🎨 Workflow Visualization

Generate visual representations of your agent workflows:

```bash
# Visualize a workflow configuration
python src/ai_agents/workflows/visualizer/render_graph.py src/ai_agents/agents/sales_assistant/config.yaml

# This generates workflow_graph.png showing:
# - Workflow nodes and their types
# - Transitions between nodes
# - Entry and exit points
# - Error handling paths
```

## 🧪 Testing

### Running Tests
```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=ai_agents

# Run specific test files
poetry run pytest tests/test_agents.py
poetry run pytest tests/test_tools.py

# Run with verbose output
poetry run pytest -v
```

### Writing Tests
```python
import pytest
from ai_agents.agents.registry import get_agent

def test_sales_assistant():
    agent = get_agent("sales_assistant")
    result = agent.run({"customer_query": "test query"})
    assert "result" in result
    assert result["result"] is not None
```

## 📝 Prompt Templates and Constructors

The framework supports flexible prompt generation through templates and constructors:

### Prompt Templates
Place `.tpl` files in your agent's `prompts/` directory:

```text
# src/ai_agents/agents/my_agent/prompts/start_node.tpl
You are a helpful assistant. Process the following request:

User Query: $user_query
Context: $context

Please provide a comprehensive response.
```

### Prompt Constructors
Define custom prompt logic in your handler module:

```python
# In your agent's handlers.py
def start_node_prompt(state, prompt_template):
    from ai_agents.workflows.core.prompts import render_prompt

    # Render base template
    base_prompt = render_prompt(prompt_template, state) if prompt_template else ""

    # Add custom logic
    if state.get("priority") == "high":
        return f"[URGENT] {base_prompt}"

    return base_prompt
```

The system automatically uses prompt constructors when available, falling back to template rendering.

## 💾 Memory Systems

### Redis Memory
Key-value storage for session data and simple persistence:

```python
# Configuration
memory:
  type: redis
  config:
    host: localhost
    port: 6379
    db: 0
    namespace: my_agent

# Usage in handlers
def my_handler(state, memory=None, **kwargs):
    if memory:
        # Store data
        memory.store("user_session", state.get("user_id"))

        # Retrieve data
        session = memory.retrieve("user_session")

        # Search (basic pattern matching)
        results = memory.search("user_*")
```

### Vector Store Memory
Semantic search and retrieval for knowledge-based agents:

```python
# Configuration
memory:
  type: vector
  config:
    embedding_model: openai
    store_path: ./vector_store

# Usage in handlers
def my_handler(state, memory=None, **kwargs):
    if memory:
        # Store documents with embeddings
        memory.store("product_info", {
            "content": "Our premium product offers...",
            "metadata": {"category": "products", "type": "premium"}
        })

        # Semantic search
        results = memory.search("premium products", limit=5)
```

## 🔧 Tools and Integrations

### Built-in Tools

#### Search Tool
Web search capabilities using DuckDuckGo:
```python
from ai_agents.tools.search import SearchTool

search_tool = SearchTool()
results = search_tool.run({"query": "latest AI developments"})
```

#### Wikipedia Tool
Wikipedia integration for research agents:
```python
from langchain_community.tools import WikipediaQueryRun
from langchain_community.utilities.wikipedia import WikipediaAPIWrapper

wiki_tool = WikipediaQueryRun(api_wrapper=WikipediaAPIWrapper())
```

### Custom Tools
Create custom tools by inheriting from `BaseTool`:

```python
from ai_agents.tools.base import BaseTool
from typing import Dict, Any

class DatabaseTool(BaseTool):
    @property
    def name(self) -> str:
        return "database_query"

    @property
    def description(self) -> str:
        return "Execute database queries and return results"

    def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        query = input_data.get("query")
        # Execute database query logic here
        return {"results": "query_results"}

# Register the tool
from ai_agents.tools.tool_registry import ToolRegistry
ToolRegistry.register(DatabaseTool)
```

## 📦 Dependencies

### Core Dependencies
- **LangGraph**: Workflow orchestration and state management
- **LangChain**: LLM integration and tool ecosystem
- **OpenAI**: Language model API client
- **Pydantic**: Data validation and settings management
- **Redis**: In-memory data structure store
- **Streamlit**: Web application framework

### Development Dependencies
- **Poetry**: Dependency management and packaging
- **Pytest**: Testing framework
- **Black**: Code formatting
- **Flake8**: Code linting
- **MyPy**: Static type checking
- **Pre-commit**: Git hooks for code quality

### Optional Dependencies
- **PyODBC**: Database connectivity
- **OpenPyXL**: Excel file processing
- **Graphviz**: Workflow visualization
- **DuckDuckGo-Search**: Web search integration
- **Wikipedia**: Wikipedia API integration

## 🔍 Adding New Agents

Follow these steps to create a new agent:

### 1. Create Agent Structure
```bash
mkdir -p src/ai_agents/agents/my_agent
touch src/ai_agents/agents/my_agent/__init__.py
touch src/ai_agents/agents/my_agent/config.yaml
touch src/ai_agents/agents/my_agent/handlers.py
mkdir -p src/ai_agents/agents/my_agent/prompts
```

### 2. Define Agent Class
```python
# src/ai_agents/agents/my_agent/__init__.py
from ai_agents.agents.workflow_agent import WorkflowAgent

class MyAgent(WorkflowAgent):
    default_config_path = "src/ai_agents/agents/my_agent/config.yaml"

    @property
    def handler_module(self):
        from . import handlers
        return handlers

    @property
    def prompt_dir(self):
        return "src/ai_agents/agents/my_agent/prompts"
```

### 3. Configure Workflow
```yaml
# src/ai_agents/agents/my_agent/config.yaml
workflow:
  entrypoint: start
  nodes:
    - name: start
      handler: start_node
      type: entry
    - name: finish
      handler: end_node
      type: exit
  edges:
    - from: start
      to: finish

state_schema:
  input: string
  output: string

model:
  type: openai
  model_id: gpt-4
```

### 4. Implement Handlers
```python
# src/ai_agents/agents/my_agent/handlers.py
def start_node(state, model=None, **kwargs):
    # Process input and generate response
    response = model.invoke(f"Process: {state.get('input')}")
    return {"output": response.content}

def end_node(state, **kwargs):
    return state
```

### 5. Register Agent
```python
# Add to src/ai_agents/agents/registry.py
from ai_agents.agents.my_agent import MyAgent

AGENT_REGISTRY = {
    # ... existing agents
    "my_agent": MyAgent,
}
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Module Import Errors
```bash
# Ensure PYTHONPATH is set correctly
export PYTHONPATH=src
# Or add to .env file
echo "PYTHONPATH=src" >> .env
```

#### 2. OpenAI API Key Issues
```bash
# Verify API key is set
echo $OPENAI_API_KEY

# Check .env file exists and contains key
cat .env | grep OPENAI_API_KEY
```

#### 3. Redis Connection Issues
```bash
# Check Redis is running
redis-cli ping

# Verify Redis configuration
redis-cli info server
```

#### 4. Memory/Performance Issues
- Reduce `max_tokens` in model configuration
- Use smaller embedding models for vector stores
- Implement result caching in custom tools
- Monitor memory usage with batch processing

### Debug Mode
Enable verbose logging for troubleshooting:
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Run agent with verbose output
from ai_agents.execution.runner import run_agent
result = run_agent(agent, input_data, verbose=True)
```

## 🤝 Contributing

We welcome contributions! Please follow these guidelines:

### Development Setup
```bash
# Fork and clone the repository
git clone https://github.com/your-username/ai-agents.git
cd ai-agents

# Install development dependencies
poetry install --with dev

# Install pre-commit hooks
pre-commit install
```

### Code Quality Standards
- **Code Formatting**: Use Black for consistent formatting
- **Linting**: Follow Flake8 guidelines
- **Type Hints**: Add type annotations for all functions
- **Documentation**: Include docstrings for all public methods
- **Testing**: Write tests for new features and bug fixes

### Contribution Process
1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Make** your changes following code quality standards
4. **Add** tests for new functionality
5. **Run** the test suite (`pytest`)
6. **Commit** your changes (`git commit -m 'Add amazing feature'`)
7. **Push** to your branch (`git push origin feature/amazing-feature`)
8. **Submit** a Pull Request

### Testing Your Changes
```bash
# Run all tests
pytest

# Run with coverage report
pytest --cov=ai_agents --cov-report=html

# Run specific test categories
pytest tests/test_agents.py -v
pytest tests/test_tools.py -v

# Run linting
flake8 src/
black --check src/
mypy src/
```

## 📚 Documentation

- **[Architecture Overview](docs/architecture.md)**: Detailed system architecture
- **[Agent Development Guide](docs/how_to_add_agent.md)**: Step-by-step agent creation
- **[Running Agents](docs/running.md)**: Execution and deployment guide
- **[Agent Types](docs/agents.md)**: Overview of built-in agents

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **LangGraph** team for the excellent workflow orchestration framework
- **LangChain** community for the comprehensive LLM ecosystem
- **OpenAI** for providing powerful language models
- All contributors who help improve this framework

---

**Built with ❤️ by the AI Agents Framework team**

For questions, issues, or contributions, please visit our [GitHub repository](https://github.com/your-org/ai-agents) or contact the maintainers.
