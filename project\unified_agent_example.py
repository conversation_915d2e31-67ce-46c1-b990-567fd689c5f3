#!/usr/bin/env python3
"""
Example usage of the Unified ReAct Agent that combines GitLab and SQL capabilities.

This script demonstrates how to use the agent to:
1. Query GitLab repositories for file content and metadata
2. Query SQL Server databases for test execution data
3. Handle mixed queries that might involve both systems

Requirements:
- GitLab access token and project configuration
- SQL Server database connection (optional)
- Mistral API key for LLM operations
"""

import os
from dotenv import load_dotenv
from agent import UnifiedReActAgent
from config import GitLabConfig, MistralConfig, AgentConfig, validate_config, print_config_summary

def main():
    """Main function to demonstrate the unified agent"""

    print("🚀 Unified GitLab & SQL ReAct Agent Demo")
    print("=" * 50)

    # Validate and load configuration
    try:
        validate_config()
        print_config_summary()

        # Get configuration
        gitlab_config = GitLabConfig.to_dict()

        # Create the unified agent
        agent = UnifiedReActAgent(
            gitlab_config=gitlab_config,
            mistral_api_key=MistralConfig.API_KEY,
            mistral_model=MistralConfig.MODEL,
            enable_sql=AgentConfig.ENABLE_SQL
        )
        print("✅ Agent initialized successfully!")

        if agent.enable_sql:
            print("✅ SQL database connection established")
        else:
            print("⚠️  SQL database not available - GitLab-only mode")

    except ValueError as e:
        print(f"❌ Configuration Error: {e}")
        print("Please check your configuration in config.py or create a .env file")
        return
    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}")
        return
    
    # Example queries
    example_queries = [
        # GitLab queries
        {
            "category": "GitLab",
            "query": "Show me the content of README.md from main branch",
            "description": "Retrieve file content from GitLab repository"
        },
        {
            "category": "GitLab", 
            "query": "What's the SHA of .gitlab-ci.yml on main branch?",
            "description": "Get file SHA hash from specific branch"
        },
        
        # SQL queries (only if SQL is enabled)
        {
            "category": "SQL",
            "query": "Show all executions for testcase ID 1",
            "description": "Query test execution data from database"
        },
        {
            "category": "SQL",
            "query": "What are the most recent commit references for testcase ID 3?",
            "description": "Get recent commit data for specific testcase"
        }
    ]
    
    # Run example queries
    print("\n📋 Running Example Queries:")
    print("-" * 30)
    
    for i, example in enumerate(example_queries, 1):
        # Skip SQL queries if SQL is not enabled
        if example["category"] == "SQL" and not agent.enable_sql:
            print(f"\n{i}. [SKIPPED - SQL not available] {example['query']}")
            continue
            
        print(f"\n{i}. [{example['category']}] {example['query']}")
        print(f"   Description: {example['description']}")
        print("   " + "-" * 40)
        
        try:
            response = agent.process_query(example["query"])
            print(f"   Response: {response}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Interactive mode
    print("\n" + "=" * 50)
    print("🎯 Interactive Mode")
    print("You can ask questions about:")
    print("  • GitLab files (content, SHA hashes, branches)")
    if agent.enable_sql:
        print("  • SQL database (test executions, testcases, runs)")
    print("  • Type 'help' for examples, 'quit' to exit")
    print("=" * 50)
    
    while True:
        try:
            user_input = input("\n💬 Your query: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            elif user_input.lower() == 'help':
                print_help(agent.enable_sql)
                continue
            elif not user_input:
                continue
            
            print("🤔 Processing...")
            response = agent.process_query(user_input)
            print(f"📝 Response: {response}")
            
        except KeyboardInterrupt:
            print("\n👋 Exiting...")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def print_help(sql_enabled=True):
    """Print help information with example queries"""
    print("\n📚 Example Queries:")
    print("\n🔧 GitLab Operations:")
    print("  • Show me the content of README.md from main branch")
    print("  • Get SHA for utils.py on develop branch")
    print("  • What's in config.py from main branch as it was on 2024-01-15T10:30:00Z?")
    
    if sql_enabled:
        print("\n🗄️  SQL Database Operations:")
        print("  • Show all executions for testcase ID 1")
        print("  • What are the most recent commit references for testcase ID 3?")
        print("  • Retrieve result, duration, and timestamp for execution of testcase ID 2")
        print("  • List all testcases in the database")
    
    print("\n💡 Tips:")
    print("  • Be specific about file paths and branch names for GitLab queries")
    print("  • Use exact table and column names for SQL queries")
    print("  • The agent will guide you through multi-step operations")

if __name__ == "__main__":
    main()
