import os
import urllib
import re
import json
import ast
from datetime import datetime
from typing import List, Optional, Any, Union
from dotenv import load_dotenv

# LangChain imports
from langchain_core.language_models.chat_models import SimpleChatModel
from langchain_core.messages import HumanMessage, AIMessage
from langchain_community.utilities import SQLDatabase
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain.tools import tool
from langchain.agents import create_react_agent, AgentExecutor
from langchain.agents import Tool
from langchain.prompts import PromptTemplate
from pydantic import BaseModel
from mistralai.client import MistralClient

# --- Configuration Section ---
class Config:
    """Centralized configuration for the application"""
    DB_TABLES = ["execution", "testcase", "run"]
    MAX_ITERATIONS = 150
    EXECUTION_TIMEOUT = 300.0
    SQL_DIALECT = "mssql"
    TOP_K = 5

# --- Environment Setup ---
def setup_environment():
    """Initialize environment variables"""
    load_dotenv()
    return os.getenv("MISTRAL_API_KEY")

# --- Core Components ---
class CustomMistralLLM(SimpleChatModel, BaseModel):
    """Custom Mistral LLM wrapper with identical functionality to original"""
    client: MistralClient
    model: str

    def __init__(self, api_key: str, model: str):
        super().__init__(client=MistralClient(api_key=api_key), model=model)

    def _call(self, messages: List[HumanMessage], stop: Optional[List[str]] = None, **kwargs: Any) -> str:    
        kwargs["temperature"] = 0

        converted = []
        for m in messages:
            if isinstance(m, HumanMessage):
                converted.append({"role": "user", "content": m.content})
            elif isinstance(m, AIMessage):
                converted.append({"role": "assistant", "content": m.content})
        result = self.client.chat(model=self.model, messages=converted)
        return result.choices[0].message.content

    @property
    def _llm_type(self) -> str:
        return "my-chat-mistral"

def create_database_connection():
    """Create and return SQLDatabase connection"""
    params = urllib.parse.quote_plus(
        r"DRIVER={ODBC Driver 17 for SQL Server};"
        r"SERVER=localhost;"
        r"DATABASE=STAGE;"
        r"Trusted_Connection=yes;"
    )
    return SQLDatabase.from_uri(
        f"mssql+pyodbc:///?odbc_connect={params}",
        include_tables=Config.DB_TABLES
    )




@tool
def final_answer_tool_func(input: Union[str, List]) -> str:
    """Tool to format final output, handling list or string input"""
    print("[DEBUG FINAL ANSWER INPUT RAW]:", input)

    if isinstance(input, list):
        # Input is likely a list of rows from SQL query
        # Convert to a readable table format or JSON string

        # Simple table format:
        output_lines = []
        for row in input:
            # Convert row list to a pipe-separated string
            line = " | ".join(str(cell) for cell in row)
            output_lines.append(line)
        output = "\n".join(output_lines)

    elif isinstance(input, str):
        # Current cleaning logic for string input
        cleaned = []
        for line in input.strip().splitlines():
            line = line.strip()
            if line.startswith("|") and "|" in line:
                parts = [cell.strip() for cell in line.split("|") if cell.strip()]
                if parts:
                    cleaned.append(" | ".join(parts))
            elif line:
                cleaned.append(line)
        output = "\n".join(cleaned)
    else:
        # Fallback: convert to string
        output = str(input)

    print("[DEBUG FINAL ANSWER CLEANED]:", output)
    return output


# --- Agent Setup ---
def create_agent_prompt():
    """Create the prompt template"""
    template = """
Do not format SQL code using markdown or triple backticks.

You are an expert SQL assistant working with a real SQL Server database. You have access to the following tools: {tools}.

Your job is to answer user questions using ONLY these tools.
Never make assumptions or guess. Always retrieve real data from the database using the tools provided.

Strict Rules to follow:
1. Never make up or guess any value (e.g., `sha`, `status`, `duration`, etc.).
2. Start by using  `sql_db_schema` 
3. Use `sql_db_query_checker` to verify any SQL query before running it.
4. Use `sql_db_query` to retrieve real data.
5. Use JOINs between tables only when needed and based on schema relationships.
6. When finished, respond using the exact Observation from the last SQL query result.
7. Do NOT summarize, paraphrase, or reformat the final SQL output.

When you have your final answer (based on the last SQL query),  
respond ONLY by calling the tool `final_answer_tool` with that exact text, and then STOP.  

Do NOT run any further SQL queries or use other tools after the final answer.  
Do NOT interpret or modify the final answer text.
Use this format:

Question: the input question
Thought: explain what you will do next
Action: the tool you want to use (choose from: {tool_names})
Action Input: the input for that tool
Observation: the output from the tool

Repeat the Thought → Action → Action Input → Observation steps as needed.

When you have your final answer (based on the last SQL query), reply like this:

Action: final_answer_tool  
Action Input: <EXACT Observation result from sql_db_query — do NOT modify it>

Use SQL dialect: {dialect}  
User query: {input}

{agent_scratchpad}
"""
    return PromptTemplate(
        input_variables=[
            "input",
            "agent_scratchpad",
            "tool_names",
            "tools",   
            "dialect",
            "top_k",
        ],
        template=template
    ).partial(dialect=Config.SQL_DIALECT, top_k=Config.TOP_K)

def setup_agent_executor(llm, db):
    """Configure and return the agent executor"""
    toolkit = SQLDatabaseToolkit(db=db, llm=llm)
    tools = toolkit.get_tools() + [
        Tool(
            name="final_answer_tool",
            func=final_answer_tool_func,
            description="Use this tool to provide the final answer to the user and stop.",
            return_direct=True  
        ),
        # compare_commit_refs
    ]
    
    agent = create_react_agent(llm, tools, prompt=create_agent_prompt())
    
    return AgentExecutor(
        agent=agent,
        tools=tools,
        verbose=True,
        handle_parsing_errors=True,
        max_iterations=Config.MAX_ITERATIONS,
        max_execution_time=Config.EXECUTION_TIMEOUT  
    )

# --- Main Execution ---
if __name__ == "__main__":
    # Initialize components
    MISTRAL_API_KEY = setup_environment()
    llm = CustomMistralLLM(api_key=MISTRAL_API_KEY, model="mistral-large-latest")
    db = create_database_connection()
    
    # Create and run agent
    agent_executor = setup_agent_executor(llm, db)
    
    # example_query = (
    #     "What are the two most recent commit references for testcase ID 3, "
    #     "and retrieve result, duration, and timestamp for each commit to compare them."
    # )
#     example_query = (
#     "Retrieve the commit reference, result, duration, and run timestamp "
#     "for the execution of testcase ID 2 in run ID 2."
# )
    example_query= (
         "Show all the execution of the test case with id=1"
     )
    
    response = agent_executor.invoke({"input": example_query})
    print(response["output"])