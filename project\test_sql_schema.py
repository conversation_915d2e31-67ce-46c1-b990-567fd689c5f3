#!/usr/bin/env python3
"""
Test SQL schema tool to debug the quote issue.
"""

from agent import create_database_connection
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from agent import CustomMistralLLM
from config import MistralConfig

def test_sql_schema():
    """Test SQL schema tool with different inputs"""
    
    # Create database and toolkit
    db = create_database_connection()
    sql_llm = CustomMistralLLM(api_key=MistralConfig.API_KEY, model='mistral-large-latest')
    sql_toolkit = SQLDatabaseToolkit(db=db, llm=sql_llm)
    sql_tools = sql_toolkit.get_tools()

    print('Testing SQL schema tool directly...')

    # Find the schema tool
    for tool in sql_tools:
        if tool.name == 'sql_db_schema':
            print(f'Testing schema tool with different inputs...')
            
            # Test with single table
            try:
                result = tool._run('testcase')
                print(f'✅ testcase result: {result[:200]}...')
            except Exception as e:
                print(f'❌ testcase error: {e}')
            
            # Test with quoted table
            try:
                quoted_input = '"testcase"'
                result = tool._run(quoted_input)
                print(f'✅ quoted testcase result: {result[:200]}...')
            except Exception as e:
                print(f'❌ quoted testcase error: {e}')
            
            # Test with multiple tables
            try:
                result = tool._run('execution,testcase,run')
                print(f'✅ multiple tables result: {result[:200]}...')
            except Exception as e:
                print(f'❌ multiple tables error: {e}')
            
            # Test with quoted multiple tables
            try:
                quoted_multiple = '"execution,testcase,run"'
                result = tool._run(quoted_multiple)
                print(f'✅ quoted multiple tables result: {result[:200]}...')
            except Exception as e:
                print(f'❌ quoted multiple tables error: {e}')
            
            break

if __name__ == "__main__":
    test_sql_schema()
