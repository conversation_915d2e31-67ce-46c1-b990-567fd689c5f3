#!/usr/bin/env python3
"""
Debug script to test agent tools directly and identify the issue.
"""

import os
import sys
from agent import UnifiedReActAgent
from config import GitLabConfig, MistralConfig, AgentConfig

def test_agent_tools():
    """Test agent tools directly"""
    print("🔧 Debug Agent Tools")
    print("=" * 50)
    
    try:
        # Create agent
        print("1. Creating agent...")
        agent = UnifiedReActAgent(
            gitlab_config=GitLabConfig.to_dict(),
            mistral_api_key=MistralConfig.API_KEY,
            mistral_model=MistralConfig.MODEL,
            enable_sql=False
        )
        print("✅ Agent created successfully")
        
        # Check current working directory
        print(f"\n2. Current working directory: {os.getcwd()}")
        
        # Check if folders exist
        from config import FileConfig
        print(f"3. PCAP folder: {FileConfig.PCAP_FOLDER}")
        print(f"   Exists: {os.path.exists(FileConfig.PCAP_FOLDER)}")
        print(f"   Is directory: {os.path.isdir(FileConfig.PCAP_FOLDER)}")
        
        if os.path.exists(FileConfig.PCAP_FOLDER):
            files = os.listdir(FileConfig.PCAP_FOLDER)
            print(f"   Files in folder: {files}")
        
        # Test file tools directly
        print("\n4. Testing file tools directly...")
        from file_tools import list_pcap_files
        try:
            pcap_files = list_pcap_files(FileConfig.PCAP_FOLDER)
            print(f"   Direct call result: Found {len(pcap_files)} files")
            for f in pcap_files[:3]:
                print(f"     - {f['filename']}")
        except Exception as e:
            print(f"   Direct call error: {e}")
        
        # Test agent tools
        print("\n5. Testing agent tools...")
        for tool in agent.tools:
            if tool.name == 'list_pcap_files':
                print(f"   Found tool: {tool.name}")
                try:
                    result = tool.func('*.pcap')
                    print(f"   Tool result type: {type(result)}")
                    print(f"   Tool result length: {len(result)}")
                    print(f"   Tool result preview: {result[:100]}...")
                    
                    # Try to parse as JSON
                    import json
                    try:
                        parsed = json.loads(result)
                        print(f"   Parsed JSON: {len(parsed)} items")
                    except json.JSONDecodeError as e:
                        print(f"   JSON parse error: {e}")
                        
                except Exception as e:
                    print(f"   Tool execution error: {e}")
                    import traceback
                    traceback.print_exc()
                break
        else:
            print("   ❌ list_pcap_files tool not found!")
        
        # Test with agent executor
        print("\n6. Testing with agent executor...")
        try:
            # Create a simple test that bypasses the LLM
            from langchain_core.tools import Tool
            
            # Find the list_pcap_files tool
            pcap_tool = None
            for tool in agent.tools:
                if tool.name == 'list_pcap_files':
                    pcap_tool = tool
                    break
            
            if pcap_tool:
                print("   Testing tool execution...")
                result = pcap_tool.func('*.pcap')
                print(f"   Result: {result[:200]}...")
            else:
                print("   ❌ Tool not found")
                
        except Exception as e:
            print(f"   Agent executor error: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_simple_query():
    """Test a simple query with debugging"""
    print("\n🤖 Testing Simple Query")
    print("=" * 50)
    
    try:
        from agent import UnifiedReActAgent
        from config import GitLabConfig, MistralConfig, AgentConfig
        
        # Create agent
        agent = UnifiedReActAgent(
            gitlab_config=GitLabConfig.to_dict(),
            mistral_api_key=MistralConfig.API_KEY,
            mistral_model=MistralConfig.MODEL,
            enable_sql=False
        )
        
        # Enable more verbose output
        agent.agent_executor.verbose = True
        
        print("Testing query: 'List PCAP files'")
        response = agent.process_query('List PCAP files')
        print(f"Final response: {response}")
        
    except Exception as e:
        print(f"❌ Query test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_agent_tools()
    test_simple_query()
