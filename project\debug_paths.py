#!/usr/bin/env python3
"""
Debug script to check path resolution issues.
"""

import os
import glob
from file_tools import make_file_tools, create_file_config, list_pcap_files
from config import FileConfig

def debug_paths():
    """Debug path resolution"""
    print("🔍 Debugging Path Resolution")
    print("=" * 50)
    
    print(f"Current working directory: {os.getcwd()}")
    print(f"FileConfig.PCAP_FOLDER: {repr(FileConfig.PCAP_FOLDER)}")
    print(f"FileConfig.LOG_FOLDER: {repr(FileConfig.LOG_FOLDER)}")
    
    # Test absolute paths
    pcap_abs = os.path.abspath(FileConfig.PCAP_FOLDER)
    print(f"Absolute PCAP path: {pcap_abs}")
    print(f"PCAP folder exists: {os.path.exists(pcap_abs)}")
    
    if os.path.exists(pcap_abs):
        files = os.listdir(pcap_abs)
        print(f"Files in PCAP folder: {files}")
        
        # Test glob patterns
        print("\nTesting glob patterns:")
        for pattern in ["*.pcap", "*.pcapng", "*.cap"]:
            search_pattern = os.path.join(pcap_abs, pattern)
            matches = glob.glob(search_pattern)
            print(f"  {pattern}: {len(matches)} matches")
            for match in matches:
                print(f"    - {os.path.basename(match)}")
    
    # Test file tools directly
    print("\n" + "=" * 30)
    print("Testing file tools directly:")
    
    try:
        result = list_pcap_files(FileConfig.PCAP_FOLDER, "*.pcap")
        print(f"list_pcap_files result: {len(result)} files")
        for f in result:
            print(f"  - {f['filename']}")
    except Exception as e:
        print(f"list_pcap_files error: {e}")
        import traceback
        traceback.print_exc()
    
    # Test tool factory
    print("\n" + "=" * 30)
    print("Testing tool factory:")
    
    try:
        file_config = create_file_config(FileConfig.PCAP_FOLDER, FileConfig.LOG_FOLDER)
        print(f"File config: {file_config}")
        
        (list_pcap_func, list_log_func, get_pcap_info_func, 
         get_log_content_func, search_logs_func) = make_file_tools(file_config)
        
        print("Testing list_pcap_func...")
        result = list_pcap_func("*.pcap")
        print(f"Result type: {type(result)}")
        print(f"Result length: {len(result)}")
        print(f"Result content: {result}")
        
        # Try to parse as JSON
        import json
        try:
            parsed = json.loads(result)
            print(f"Parsed JSON: {len(parsed)} items")
        except json.JSONDecodeError as e:
            print(f"JSON parse error: {e}")
            
    except Exception as e:
        print(f"Tool factory error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_paths()
