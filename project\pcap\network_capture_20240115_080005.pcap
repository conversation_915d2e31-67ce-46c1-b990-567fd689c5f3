This is a placeholder for a PCAP file.

In a real scenario, this would be a binary file containing network packet data captured using tools like:
- Wireshark
- tcpdump
- tshark
- Network monitoring tools

This file would contain:
- Ethernet frames
- IP packets
- TCP/UDP segments
- Application layer data (HTTP, HTTPS, DNS, etc.)

File details:
- Capture start: 2024-01-15 08:00:05
- Capture duration: 1 hour
- Packets captured: 15,847
- File size: ~25 MB
- Protocols: TCP (89%), UDP (8%), ICMP (3%)
- Top ports: 80 (45%), 443 (35%), 8080 (12%), 22 (5%)

To create real PCAP files, you would use commands like:
tcpdump -i eth0 -w network_capture.pcap
wireshark -i eth0 -w network_capture.pcap
