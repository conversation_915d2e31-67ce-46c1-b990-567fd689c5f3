#!/usr/bin/env python3
"""
Test script for the Unified GitLab & SQL ReAct Agent.

This script performs basic tests to ensure the agent is working correctly.
"""

import sys
import traceback
from agent import UnifiedReActAgent
from config import GitLabConfig, <PERSON><PERSON><PERSON><PERSON>onfig, AgentConfig, validate_config

def test_configuration():
    """Test configuration validation"""
    print("🔧 Testing Configuration...")
    try:
        validate_config()
        print("✅ Configuration is valid")
        return True
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_agent_initialization():
    """Test agent initialization"""
    print("\n🤖 Testing Agent Initialization...")
    try:
        gitlab_config = GitLabConfig.to_dict()
        agent = UnifiedReActAgent(
            gitlab_config=gitlab_config,
            mistral_api_key=MistralConfig.API_KEY,
            mistral_model=MistralConfig.MODEL,
            enable_sql=AgentConfig.ENABLE_SQL
        )
        print("✅ Agent initialized successfully")
        return agent
    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
        traceback.print_exc()
        return None

def test_gitlab_functionality(agent):
    """Test GitLab functionality"""
    print("\n📁 Testing GitLab Functionality...")
    
    # Simple GitLab query
    test_query = "What's the SHA of .gitlab-ci.yml on main branch?"
    
    try:
        print(f"Query: {test_query}")
        response = agent.process_query(test_query)
        print(f"Response: {response}")
        
        if "error" in response.lower() or "failed" in response.lower():
            print("⚠️  GitLab test completed with errors")
            return False
        else:
            print("✅ GitLab functionality working")
            return True
            
    except Exception as e:
        print(f"❌ GitLab test failed: {e}")
        return False

def test_sql_functionality(agent):
    """Test SQL functionality (if enabled)"""
    if not agent.enable_sql:
        print("\n🗄️  SQL functionality disabled - skipping test")
        return True
        
    print("\n🗄️  Testing SQL Functionality...")
    
    # Simple SQL query
    test_query = "Show me the database schema"
    
    try:
        print(f"Query: {test_query}")
        response = agent.process_query(test_query)
        print(f"Response: {response}")
        
        if "error" in response.lower() or "failed" in response.lower():
            print("⚠️  SQL test completed with errors")
            return False
        else:
            print("✅ SQL functionality working")
            return True
            
    except Exception as e:
        print(f"❌ SQL test failed: {e}")
        return False

def test_mixed_queries(agent):
    """Test the agent's ability to handle different types of queries"""
    print("\n🔀 Testing Mixed Query Handling...")
    
    test_queries = [
        "Extract info from this query: Show me .gitlab-ci.yml from main branch",
        "What tools are available to me?",
    ]
    
    success_count = 0
    
    for i, query in enumerate(test_queries, 1):
        try:
            print(f"\nTest {i}: {query}")
            response = agent.process_query(query)
            print(f"Response: {response[:200]}..." if len(response) > 200 else f"Response: {response}")
            success_count += 1
            
        except Exception as e:
            print(f"❌ Test {i} failed: {e}")
    
    if success_count == len(test_queries):
        print("✅ All mixed query tests passed")
        return True
    else:
        print(f"⚠️  {success_count}/{len(test_queries)} mixed query tests passed")
        return False

def run_all_tests():
    """Run all tests"""
    print("🧪 Running Unified Agent Tests")
    print("=" * 50)
    
    # Test configuration
    if not test_configuration():
        print("\n❌ Configuration test failed. Please fix configuration before proceeding.")
        return False
    
    # Test agent initialization
    agent = test_agent_initialization()
    if not agent:
        print("\n❌ Agent initialization failed. Cannot proceed with further tests.")
        return False
    
    # Test GitLab functionality
    gitlab_success = test_gitlab_functionality(agent)
    
    # Test SQL functionality
    sql_success = test_sql_functionality(agent)
    
    # Test mixed queries
    mixed_success = test_mixed_queries(agent)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    tests = [
        ("Configuration", True),
        ("Agent Initialization", True),
        ("GitLab Functionality", gitlab_success),
        ("SQL Functionality", sql_success),
        ("Mixed Query Handling", mixed_success)
    ]
    
    passed = sum(1 for _, success in tests if success)
    total = len(tests)
    
    for test_name, success in tests:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The agent is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the configuration and dependencies.")
        return False

def interactive_test():
    """Run an interactive test session"""
    print("\n🎯 Interactive Test Mode")
    print("Enter queries to test the agent. Type 'quit' to exit.")
    print("-" * 40)
    
    try:
        gitlab_config = GitLabConfig.to_dict()
        agent = UnifiedReActAgent(
            gitlab_config=gitlab_config,
            mistral_api_key=MistralConfig.API_KEY,
            mistral_model=MistralConfig.MODEL,
            enable_sql=AgentConfig.ENABLE_SQL
        )
        
        while True:
            try:
                query = input("\n💬 Test query: ").strip()
                
                if query.lower() in ['quit', 'exit', 'q']:
                    break
                elif not query:
                    continue
                
                print("🤔 Processing...")
                response = agent.process_query(query)
                print(f"📝 Response: {response}")
                
            except KeyboardInterrupt:
                print("\n👋 Exiting interactive test...")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                
    except Exception as e:
        print(f"❌ Failed to start interactive test: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_test()
    else:
        success = run_all_tests()
        
        if success:
            print("\n🚀 Ready to run the full agent!")
            print("Run 'python unified_agent_example.py' for the full demo")
            print("Or run 'python test_agent.py --interactive' for interactive testing")
        else:
            print("\n🔧 Please fix the issues above before using the agent")
            sys.exit(1)
