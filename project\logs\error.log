2024-01-15 08:00:30.123 [ERROR] DatabaseManager - Connection timeout while executing query
2024-01-15 08:00:30.234 [ERROR] DatabaseManager - SQLException: Connection is not available, request timed out after 30000ms
2024-01-15 08:00:30.235 [ERROR] DatabaseManager - Stack trace:
java.sql.SQLException: Connection is not available, request timed out after 30000ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:695)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariDataSource.getConnection(HikariDataSource.java:128)
	at com.example.database.DatabaseManager.executeQuery(DatabaseManager.java:45)
	at com.example.service.UserService.getUserProfile(UserService.java:123)
2024-01-15 08:00:50.123 [ERROR] CacheManager - Redis connection lost
2024-01-15 08:00:50.234 [ERROR] CacheManager - Failed to retrieve cache key: user_session_67890
2024-01-15 08:00:50.235 [ERROR] CacheManager - Stack trace:
redis.clients.jedis.exceptions.JedisConnectionException: java.net.ConnectException: Connection refused
	at redis.clients.jedis.Connection.connect(Connection.java:207)
	at redis.clients.jedis.BinaryClient.connect(BinaryClient.java:93)
	at redis.clients.jedis.BinaryJedis.connect(BinaryJedis.java:1767)
	at com.example.cache.CacheManager.get(CacheManager.java:67)
2024-01-15 08:15:22.456 [ERROR] FileUploadService - File upload failed for user ID: 12345
2024-01-15 08:15:22.457 [ERROR] FileUploadService - IOException: No space left on device
2024-01-15 08:15:22.458 [ERROR] FileUploadService - Stack trace:
java.io.IOException: No space left on device
	at java.io.FileOutputStream.writeBytes(Native Method)
	at java.io.FileOutputStream.write(FileOutputStream.java:326)
	at com.example.upload.FileUploadService.saveFile(FileUploadService.java:89)
2024-01-15 08:30:15.789 [ERROR] AuthenticationService - Authentication failed for user: <EMAIL>
2024-01-15 08:30:15.790 [ERROR] AuthenticationService - Invalid credentials provided
2024-01-15 08:30:15.791 [ERROR] SecurityManager - Suspicious activity detected from IP: *************
2024-01-15 08:30:15.792 [ERROR] SecurityManager - Multiple failed login attempts: 15 attempts in 5 minutes
2024-01-15 08:45:33.123 [ERROR] PaymentProcessor - Payment processing failed
2024-01-15 08:45:33.124 [ERROR] PaymentProcessor - Transaction ID: TXN_789456123
2024-01-15 08:45:33.125 [ERROR] PaymentProcessor - Error: Credit card declined
2024-01-15 08:45:33.126 [ERROR] PaymentProcessor - Response code: 05 - Do not honor
2024-01-15 09:01:46.100 [ERROR] TestCase016 - Configuration file not found: /config/app.properties
2024-01-15 09:01:46.200 [ERROR] TestCase016 - Unable to load application configuration
2024-01-15 09:01:46.201 [ERROR] ConfigurationManager - FileNotFoundException: /config/app.properties (No such file or directory)
2024-01-15 09:01:46.202 [ERROR] ConfigurationManager - Stack trace:
java.io.FileNotFoundException: /config/app.properties (No such file or directory)
	at java.io.FileInputStream.open0(Native Method)
	at java.io.FileInputStream.open(FileInputStream.java:195)
	at java.io.FileInputStream.<init>(FileInputStream.java:138)
	at com.example.config.ConfigurationManager.loadProperties(ConfigurationManager.java:34)
2024-01-15 09:01:49.300 [ERROR] TestCase018 - SMTP connection failed: Connection refused
2024-01-15 09:01:49.400 [ERROR] TestCase018 - Unable to send email notification
2024-01-15 09:01:49.401 [ERROR] EmailService - ConnectException: Connection refused (Connection refused)
2024-01-15 09:01:49.402 [ERROR] EmailService - Stack trace:
java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at com.example.email.EmailService.sendEmail(EmailService.java:78)
2024-01-15 10:15:45.678 [ERROR] APIController - Unhandled exception in REST endpoint
2024-01-15 10:15:45.679 [ERROR] APIController - Endpoint: POST /api/users/create
2024-01-15 10:15:45.680 [ERROR] APIController - NullPointerException: Cannot invoke method on null object
2024-01-15 10:15:45.681 [ERROR] APIController - Stack trace:
java.lang.NullPointerException: Cannot invoke "com.example.model.User.setId(java.lang.Long)" because "user" is null
	at com.example.controller.UserController.createUser(UserController.java:45)
	at com.example.controller.APIController.handleRequest(APIController.java:123)
2024-01-15 10:30:12.345 [ERROR] ScheduledTaskManager - Scheduled task failed: daily_report_generation
2024-01-15 10:30:12.346 [ERROR] ScheduledTaskManager - Task execution time: 2024-01-15 10:30:00
2024-01-15 10:30:12.347 [ERROR] ReportGenerator - OutOfMemoryError: Java heap space
2024-01-15 10:30:12.348 [ERROR] ReportGenerator - Stack trace:
java.lang.OutOfMemoryError: Java heap space
	at java.util.Arrays.copyOf(Arrays.java:3210)
	at java.util.ArrayList.grow(ArrayList.java:267)
	at java.util.ArrayList.ensureExplicitCapacity(ArrayList.java:241)
	at com.example.reports.ReportGenerator.generateDailyReport(ReportGenerator.java:156)
2024-01-15 11:00:23.567 [ERROR] WebSocketManager - WebSocket connection error
2024-01-15 11:00:23.568 [ERROR] WebSocketManager - Client ID: ws_client_456789
2024-01-15 11:00:23.569 [ERROR] WebSocketManager - SocketTimeoutException: Read timed out
2024-01-15 11:00:23.570 [ERROR] WebSocketManager - Stack trace:
java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at com.example.websocket.WebSocketManager.handleConnection(WebSocketManager.java:89)
2024-01-15 11:15:44.890 [ERROR] DataProcessor - Data processing pipeline failed
2024-01-15 11:15:44.891 [ERROR] DataProcessor - Batch ID: BATCH_20240115_111544
2024-01-15 11:15:44.892 [ERROR] DataProcessor - Records processed: 15,847 of 20,000
2024-01-15 11:15:44.893 [ERROR] DataProcessor - IllegalArgumentException: Invalid data format in record 15848
2024-01-15 11:15:44.894 [ERROR] DataProcessor - Stack trace:
java.lang.IllegalArgumentException: Invalid data format: expected JSON but received XML
	at com.example.processor.DataValidator.validateFormat(DataValidator.java:67)
	at com.example.processor.DataProcessor.processRecord(DataProcessor.java:234)
2024-01-15 11:30:56.123 [ERROR] BackupService - Database backup failed
2024-01-15 11:30:56.124 [ERROR] BackupService - Backup location: /backups/db_backup_20240115_113056.sql
2024-01-15 11:30:56.125 [ERROR] BackupService - IOException: Permission denied
2024-01-15 11:30:56.126 [ERROR] BackupService - Stack trace:
java.io.IOException: Permission denied
	at java.io.UnixFileSystem.createFileExclusively(Native Method)
	at java.io.File.createNewFile(File.java:1012)
	at com.example.backup.BackupService.createBackupFile(BackupService.java:123)
2024-01-15 12:00:11.234 [ERROR] LoadBalancer - Health check failed for server: app-server-03
2024-01-15 12:00:11.235 [ERROR] LoadBalancer - Server URL: http://app-server-03:8080/health
2024-01-15 12:00:11.236 [ERROR] LoadBalancer - ConnectTimeoutException: Connect timed out
2024-01-15 12:00:11.237 [ERROR] LoadBalancer - Removing server from active pool
2024-01-15 12:15:33.456 [ERROR] MessageQueueProcessor - Message processing failed
2024-01-15 12:15:33.457 [ERROR] MessageQueueProcessor - Queue: user_notifications
2024-01-15 12:15:33.458 [ERROR] MessageQueueProcessor - Message ID: msg_789123456
2024-01-15 12:15:33.459 [ERROR] MessageQueueProcessor - SerializationException: Unable to deserialize message payload
2024-01-15 12:30:45.678 [ERROR] SecurityScanner - Security vulnerability detected
2024-01-15 12:30:45.679 [ERROR] SecurityScanner - Vulnerability type: SQL Injection attempt
2024-01-15 12:30:45.680 [ERROR] SecurityScanner - Request URL: /api/users/search?name='; DROP TABLE users; --
2024-01-15 12:30:45.681 [ERROR] SecurityScanner - Source IP: ************
2024-01-15 12:30:45.682 [ERROR] SecurityScanner - Request blocked and IP added to blacklist
