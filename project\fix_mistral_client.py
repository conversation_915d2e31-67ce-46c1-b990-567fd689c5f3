#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the Mistral client deprecation issue and test the new client.

This script will:
1. Check the current Mistral client version
2. Guide you through upgrading to the new client
3. Test the new client functionality
"""

import subprocess
import sys
import importlib.util

def check_mistral_version():
    """Check the current Mistral client version"""
    try:
        import mistralai
        version = getattr(mistralai, '__version__', 'unknown')
        print(f"Current mistralai version: {version}")
        return version
    except ImportError:
        print("❌ mistralai package not installed")
        return None

def upgrade_mistral_client():
    """Upgrade the Mistral client to the latest version"""
    print("🔄 Upgrading Mistral client...")
    
    try:
        # Uninstall old version
        subprocess.check_call([sys.executable, "-m", "pip", "uninstall", "mistralai", "-y"])
        print("✅ Old mistralai client uninstalled")
        
        # Install new version
        subprocess.check_call([sys.executable, "-m", "pip", "install", "mistralai>=1.0.0"])
        print("✅ New mistralai client installed")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to upgrade mistralai: {e}")
        return False

def test_new_mistral_client():
    """Test the new Mistral client"""
    print("\n🧪 Testing new Mistral client...")
    
    try:
        from mistralai import Mistral
        from config import MistralConfig
        
        # Create client
        client = Mistral(api_key=MistralConfig.API_KEY)
        
        # Test simple completion
        print("Testing chat completion...")
        response = client.chat.complete(
            model=MistralConfig.MODEL,
            messages=[
                {"role": "user", "content": "Hello, this is a test. Please respond with 'Test successful!'"}
            ],
            max_tokens=10
        )
        
        result = response.choices[0].message.content
        print(f"✅ Mistral client test successful!")
        print(f"Response: {result}")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("The new mistralai client may not be installed correctly.")
        return False
    except Exception as e:
        print(f"❌ Mistral client test failed: {e}")
        return False

def test_agent_with_new_client():
    """Test the agent with the new Mistral client"""
    print("\n🤖 Testing agent with new Mistral client...")
    
    try:
        from agent import UnifiedReActAgent
        from config import GitLabConfig, MistralConfig, AgentConfig
        
        # Create agent (SQL disabled for this test)
        agent = UnifiedReActAgent(
            gitlab_config=GitLabConfig.to_dict(),
            mistral_api_key=MistralConfig.API_KEY,
            mistral_model=MistralConfig.MODEL,
            enable_sql=False  # Disable SQL to focus on Mistral client test
        )
        
        print("✅ Agent created successfully with new Mistral client!")
        
        # Test a simple query
        print("Testing simple query...")
        response = agent.process_query("Extract info from this query: Show me README.md")
        print(f"✅ Agent query test successful!")
        print(f"Response: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to fix and test Mistral client"""
    print("🔧 Mistral Client Fix & Test Tool")
    print("=" * 50)
    
    # Check current version
    current_version = check_mistral_version()
    
    if current_version and current_version.startswith('0.'):
        print(f"⚠️  You have an old version ({current_version}) that is deprecated.")
        print("This is causing the warning you're seeing.")
        
        # Ask user if they want to upgrade
        response = input("\nDo you want to upgrade to the new client? (y/n): ").lower().strip()
        
        if response in ['y', 'yes']:
            if upgrade_mistral_client():
                print("\n🎉 Upgrade completed!")
                
                # Test new client
                if test_new_mistral_client():
                    print("\n🎉 New Mistral client is working!")
                    
                    # Test agent
                    if test_agent_with_new_client():
                        print("\n🎉 Agent is working with new Mistral client!")
                        print("\nYou can now run your agent without the deprecation warning.")
                    else:
                        print("\n⚠️  Agent test failed. There may be other issues to resolve.")
                else:
                    print("\n❌ New client test failed. Please check your API key and configuration.")
            else:
                print("\n❌ Upgrade failed. Please try manually:")
                print("pip uninstall mistralai")
                print("pip install mistralai>=1.0.0")
        else:
            print("\nSkipping upgrade. The deprecation warning will continue.")
            print("To fix it later, run this script again or manually upgrade:")
            print("pip uninstall mistralai && pip install mistralai>=1.0.0")
    
    elif current_version and not current_version.startswith('0.'):
        print(f"✅ You have a newer version ({current_version})")
        
        # Test the client
        if test_new_mistral_client():
            print("✅ Mistral client is working correctly!")
            
            # Test agent
            if test_agent_with_new_client():
                print("✅ Agent is working correctly!")
            else:
                print("⚠️  Agent has issues. Check your configuration.")
        else:
            print("❌ Mistral client test failed. Check your API key and configuration.")
    
    else:
        print("❌ mistralai package not found. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "mistralai>=1.0.0"])
            print("✅ mistralai installed!")
            test_new_mistral_client()
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install mistralai: {e}")

if __name__ == "__main__":
    main()
