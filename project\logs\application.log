2024-01-15 08:00:00.123 [INFO ] ApplicationStartup - Starting application server on port 8080
2024-01-15 08:00:00.456 [INFO ] DatabaseManager - Connecting to database: ***************************************
2024-01-15 08:00:01.234 [INFO ] DatabaseManager - Database connection established successfully
2024-01-15 08:00:01.567 [INFO ] SecurityManager - Loading security configurations
2024-01-15 08:00:01.890 [INFO ] SecurityManager - SSL certificates loaded
2024-01-15 08:00:02.123 [INFO ] CacheManager - Initializing Redis cache connection
2024-01-15 08:00:02.456 [INFO ] CacheManager - Cache connection established
2024-01-15 08:00:02.789 [INFO ] ApplicationStartup - Application started successfully in 2.666 seconds
2024-01-15 08:00:05.123 [INFO ] RequestHandler - Received GET request for /api/health
2024-01-15 08:00:05.234 [INFO ] RequestHandler - Health check completed successfully
2024-01-15 08:00:10.456 [INFO ] RequestHandler - Received POST request for /api/users/login
2024-01-15 08:00:10.567 [INFO ] AuthenticationService - User authentication attempt for user: <EMAIL>
2024-01-15 08:00:10.678 [INFO ] AuthenticationService - User authenticated successfully
2024-01-15 08:00:10.789 [INFO ] SessionManager - Session created for user: <EMAIL>
2024-01-15 08:00:15.123 [INFO ] RequestHandler - Received GET request for /api/users/profile
2024-01-15 08:00:15.234 [INFO ] UserService - Fetching profile for user ID: 12345
2024-01-15 08:00:15.345 [INFO ] DatabaseManager - Executing query: SELECT * FROM users WHERE id = 12345
2024-01-15 08:00:15.456 [INFO ] UserService - Profile data retrieved successfully
2024-01-15 08:00:20.123 [WARN ] MemoryMonitor - High memory usage detected: 85% of heap used
2024-01-15 08:00:25.234 [INFO ] RequestHandler - Received POST request for /api/data/upload
2024-01-15 08:00:25.345 [INFO ] FileUploadService - Processing file upload: document.pdf (2.5MB)
2024-01-15 08:00:25.456 [INFO ] FileUploadService - File validation completed
2024-01-15 08:00:25.567 [INFO ] FileUploadService - File saved to: /uploads/2024/01/15/document_12345.pdf
2024-01-15 08:00:30.123 [ERROR] DatabaseManager - Connection timeout while executing query
2024-01-15 08:00:30.234 [ERROR] DatabaseManager - SQLException: Connection is not available, request timed out after 30000ms
2024-01-15 08:00:30.345 [WARN ] DatabaseManager - Attempting to reconnect to database
2024-01-15 08:00:31.456 [INFO ] DatabaseManager - Database reconnection successful
2024-01-15 08:00:35.123 [INFO ] RequestHandler - Received GET request for /api/reports/daily
2024-01-15 08:00:35.234 [INFO ] ReportService - Generating daily report for date: 2024-01-15
2024-01-15 08:00:35.345 [INFO ] ReportService - Processing 1,247 records
2024-01-15 08:00:36.456 [INFO ] ReportService - Daily report generated successfully
2024-01-15 08:00:40.123 [WARN ] SecurityManager - Multiple failed login attempts detected from IP: *************
2024-01-15 08:00:40.234 [WARN ] SecurityManager - IP ************* temporarily blocked for 15 minutes
2024-01-15 08:00:45.123 [INFO ] RequestHandler - Received DELETE request for /api/users/12346
2024-01-15 08:00:45.234 [INFO ] UserService - Deleting user account: <EMAIL>
2024-01-15 08:00:45.345 [INFO ] UserService - User account deleted successfully
2024-01-15 08:00:50.123 [ERROR] CacheManager - Redis connection lost
2024-01-15 08:00:50.234 [ERROR] CacheManager - Failed to retrieve cache key: user_session_67890
2024-01-15 08:00:50.345 [WARN ] CacheManager - Falling back to database for session data
2024-01-15 08:00:51.456 [INFO ] CacheManager - Redis connection restored
2024-01-15 08:00:55.123 [INFO ] RequestHandler - Received PUT request for /api/settings/update
2024-01-15 08:00:55.234 [INFO ] SettingsService - Updating application settings
2024-01-15 08:00:55.345 [INFO ] SettingsService - Settings updated successfully
2024-01-15 08:00:55.456 [INFO ] ConfigurationManager - Configuration cache refreshed
2024-01-15 08:01:00.123 [INFO ] ScheduledTaskManager - Running scheduled task: cleanup_temp_files
2024-01-15 08:01:00.234 [INFO ] ScheduledTaskManager - Cleaned up 23 temporary files
2024-01-15 08:01:00.345 [INFO ] ScheduledTaskManager - Scheduled task completed in 0.222 seconds
