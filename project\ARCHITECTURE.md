# 🏗️ Unified GitLab & SQL ReAct Agent - Architecture Guide

This document explains the role of each file and how the system works together to create a powerful unified agent.

## 📁 File Structure Overview

```
📦 Unified Agent Project
├── 🤖 agent.py                    # Main agent implementation
├── 🔧 config.py                   # Configuration management
├── 🛠️ tools.py                    # GitLab API tools
├── 📁 file_tools.py               # Local file system tools (PCAP/Log)
├── 🚀 unified_agent_example.py    # Usage examples and demo
├── 🧪 test_agent.py               # Test suite
├── 🧪 test_file_tools.py          # File tools test suite
├── 🔄 fix_mistral_client.py       # Mistral client upgrade tool
├── 📋 requirements.txt            # Dependencies
├── 📖 README.md                   # User documentation
└── 📐 ARCHITECTURE.md             # This file
```

## 🔍 Detailed File Explanations

### 🤖 `agent.py` - The Heart of the System

**Role**: Main agent implementation that combines GitLab and SQL capabilities

**Key Components**:

#### 1. **LLM Wrappers**
```python
class MistralLLM(LLM):           # For GitLab operations (completion API)
class CustomMistralLLM(SimpleChatModel):  # For SQL operations (chat API)
```
- **Why Two LLMs?**: Different operations need different API patterns
- **GitLab LLM**: Uses completion API for simple file operations
- **SQL LLM**: Uses chat API for complex database reasoning

#### 2. **UnifiedReActAgent Class**
```python
class UnifiedReActAgent:
    def __init__(self, gitlab_config, mistral_api_key, enable_sql=True)
    def _create_tools(self)          # Combines GitLab + SQL tools
    def _create_react_agent(self)    # Creates the reasoning agent
    def process_query(self, query)   # Main entry point
```

**How It Works**:
1. **Initialization**: Sets up both GitLab and SQL connections
2. **Tool Creation**: Merges tools from both systems into one toolkit
3. **Agent Creation**: Creates a ReAct agent that can reason across both systems
4. **Query Processing**: Routes and executes multi-step operations

#### 3. **Database Integration**
```python
def create_database_connection():    # SQL Server connection
def final_answer_tool_func():       # Formats SQL results
```

### 🔧 `config.py` - Configuration Management

**Role**: Centralized configuration for all system components

**Key Classes**:

#### 1. **GitLabConfig**
```python
class GitLabConfig:
    GITLAB_URL = "https://gitlab.com"
    PROJECT_ID = "your_project_id"
    TOKEN = "your_gitlab_token"
```

#### 2. **SQLConfig**
```python
class SQLConfig:
    SERVER = "localhost"
    DATABASE = "STAGE"
    TABLES = ["execution", "testcase", "run"]
    
    @classmethod
    def get_connection_string(cls):  # Generates connection string
```

#### 3. **MistralConfig**
```python
class MistralConfig:
    API_KEY = "your_api_key"
    MODEL = "mistral-large-latest"
```

#### 4. **Connection Testing**
```python
def test_database_connection():   # Tests SQL Server
def test_gitlab_connection():     # Tests GitLab API
def test_mistral_connection():    # Tests Mistral AI
def run_connection_tests():       # Tests all connections
```

**Usage**:
```bash
python config.py --test-connections  # Test all
python config.py --test-db          # Test database only
```

### 🛠️ `tools.py` - GitLab API Tools

**Role**: Provides GitLab API functionality as LangChain tools

**Key Functions**:

#### 1. **Core GitLab Operations**
```python
def get_file_sha(commit_ref, file_path):           # Get file SHA hash
def get_file_sha_from_branch_ts(branch, timestamp, file_path);  # Historical SHA
def get_file_content(file_sha):                    # Get file content
```

#### 2. **Tool Factory**
```python
def make_gitlab_tools(config):
    # Returns configured GitLab API functions
    return get_sha_func, get_sha_ts_func, get_content_func
```

#### 3. **Configuration Helper**
```python
def create_gitlab_config(gitlab_url, project_id, token):
    # Creates GitLab configuration dictionary
```

**How It Works**:
- Uses GitLab REST API with authentication
- Handles Base64 decoding for file content
- Provides error handling for API failures
- Integrates with LangChain tool system

### 📁 `file_tools.py` - Local File System Tools

**Role**: Provides local file system access for PCAP and log files

**Key Functions**:

#### 1. **File Listing Operations**
```python
def list_pcap_files(pcap_folder, pattern):         # List PCAP files
def list_log_files(log_folder, pattern);           # List log files
```

#### 2. **File Content Operations**
```python
def get_pcap_file_info(filename, pcap_folder);     # Get PCAP file metadata
def get_log_file_content(filename, log_folder);    # Read log file content
def search_log_files(search_term, log_folder);     # Search across log files
```

#### 3. **Tool Factory**
```python
def make_file_tools(config):
    # Returns configured file system functions
    return list_pcap_func, list_log_func, get_pcap_info_func, get_log_content_func, search_logs_func
```

**How It Works**:
- Direct file system access using Python os/glob modules
- Supports multiple file patterns (*.pcap, *.pcapng, *.log, *.txt)
- Provides file metadata (size, timestamps, permissions)
- Handles text encoding and binary file detection
- Integrates with LangChain tool system

### 🚀 `unified_agent_example.py` - Usage Examples

**Role**: Demonstrates how to use the unified agent

**Key Features**:

#### 1. **Configuration Loading**
```python
from config import GitLabConfig, MistralConfig, AgentConfig
gitlab_config = GitLabConfig.to_dict()
```

#### 2. **Agent Initialization**
```python
agent = UnifiedReActAgent(
    gitlab_config=gitlab_config,
    mistral_api_key=MistralConfig.API_KEY,
    enable_sql=AgentConfig.ENABLE_SQL
)
```

#### 3. **Example Queries**
```python
example_queries = [
    "Show me the content of README.md from main branch",      # GitLab
    "Show all executions for testcase ID 1",                 # SQL
    "Find failed tests and show their config files"          # Combined
]
```

#### 4. **Interactive Mode**
- Real-time query processing
- Help system with examples
- Error handling and user feedback

### 🧪 `test_agent.py` - Test Suite

**Role**: Comprehensive testing of all system components

**Test Categories**:

#### 1. **Configuration Tests**
```python
def test_configuration():
    # Validates all configuration settings
    validate_config()
```

#### 2. **Agent Initialization Tests**
```python
def test_agent_initialization():
    # Tests agent creation with both GitLab and SQL
```

#### 3. **Functionality Tests**
```python
def test_gitlab_functionality(agent):    # Tests GitLab operations
def test_sql_functionality(agent):       # Tests SQL operations  
def test_mixed_queries(agent):           # Tests cross-system queries
```

#### 4. **Interactive Testing**
```python
def interactive_test():
    # Allows manual testing of queries
```

**Usage**:
```bash
python test_agent.py                    # Run all tests
python test_agent.py --interactive      # Interactive testing
```

### 🔄 `fix_mistral_client.py` - Client Upgrade Tool

**Role**: Handles Mistral client deprecation issues

**Key Functions**:

#### 1. **Version Management**
```python
def check_mistral_version():      # Check current version
def upgrade_mistral_client():     # Upgrade to new version
```

#### 2. **Testing**
```python
def test_new_mistral_client():    # Test new client API
def test_agent_with_new_client(): # Test agent with new client
```

**Why Needed**: Mistral client API changed significantly between versions

### 📋 `requirements.txt` - Dependencies

**Role**: Defines all required Python packages

**Key Dependencies**:
```
langchain                 # LLM framework and agents
langchain-community       # Community tools and utilities
mistralai>=1.0.0         # Mistral AI client (new version)
sqlalchemy               # Database ORM
pyodbc                   # SQL Server driver
requests                 # HTTP client for GitLab API
```

## 🔄 How Everything Works Together

### 1. **System Initialization Flow**

```mermaid
graph TD
    A[Load config.py] --> B[Validate Configuration]
    B --> C[Create GitLab Config]
    B --> D[Create SQL Connection]
    C --> E[Initialize UnifiedReActAgent]
    D --> E
    E --> F[Create Tool Set]
    F --> G[Create ReAct Agent]
    G --> H[Ready for Queries]
```

### 2. **Query Processing Flow**

```mermaid
graph TD
    A[User Query] --> B[ReAct Agent]
    B --> C{Determine Tools Needed}
    C -->|GitLab| D[GitLab Tools]
    C -->|SQL| E[SQL Tools]
    C -->|Both| F[Multi-step Process]
    D --> G[GitLab API Call]
    E --> H[SQL Database Query]
    F --> I[Chain GitLab + SQL]
    G --> J[Format Response]
    H --> J
    I --> J
    J --> K[Return to User]
```

### 3. **Tool Integration Architecture**

```python
# In agent.py
def _create_tools(self):
    tools = []
    
    # GitLab tools from tools.py
    gitlab_tools = [get_file_sha, get_file_content, ...]
    tools.extend(gitlab_tools)
    
    # SQL tools from LangChain
    if self.enable_sql:
        sql_tools = SQLDatabaseToolkit(db=self.db).get_tools()
        tools.extend(sql_tools)
    
    return tools  # Combined tool set
```

## 🎯 Key Design Principles

### 1. **Modularity**
- Each file has a specific responsibility
- Components can be tested independently
- Easy to extend or modify individual parts

### 2. **Configuration-Driven**
- All settings centralized in `config.py`
- Environment variable support
- Easy deployment across environments

### 3. **Error Resilience**
- Comprehensive error handling
- Graceful degradation (SQL can be disabled)
- Detailed error messages and logging

### 4. **Testability**
- Dedicated test suite
- Connection testing utilities
- Interactive testing mode

### 5. **Extensibility**
- Easy to add new tools
- Plugin-like architecture for new data sources
- Configurable agent behavior

## 🚀 Getting Started Workflow

1. **Setup**: Install dependencies and configure settings
2. **Test**: Run connection tests to verify setup
3. **Demo**: Try the example script
4. **Integrate**: Use in your own applications

This architecture provides a robust, scalable foundation for building intelligent agents that can work across multiple systems while maintaining clean separation of concerns and easy maintainability.
