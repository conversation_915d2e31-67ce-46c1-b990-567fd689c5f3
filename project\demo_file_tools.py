#!/usr/bin/env python3
"""
Demo script for the File Tools with example PCAP and log files.

This script demonstrates the file tools functionality using the example files
that have been created in the pcap and logs directories.
"""

import json
from file_tools import (
    list_pcap_files, list_log_files, get_pcap_file_info, 
    get_log_file_content, search_log_files
)
from config import FileConfig

def demo_list_files():
    """Demonstrate file listing functionality"""
    print("🗂️  File Listing Demo")
    print("=" * 50)
    
    # List PCAP files
    print("\n📁 PCAP Files:")
    try:
        pcap_files = list_pcap_files(FileConfig.PCAP_FOLDER)
        for file_info in pcap_files:
            print(f"  📄 {file_info['filename']}")
            print(f"     Size: {file_info['size_mb']} MB")
            print(f"     Modified: {file_info['modified_time']}")
            print()
    except Exception as e:
        print(f"❌ Error listing PCAP files: {e}")
    
    # List log files
    print("\n📋 Log Files:")
    try:
        log_files = list_log_files(FileConfig.LOG_FOLDER)
        for file_info in log_files:
            print(f"  📄 {file_info['filename']}")
            print(f"     Size: {file_info['size_kb']} KB")
            print(f"     Modified: {file_info['modified_time']}")
            print()
    except Exception as e:
        print(f"❌ Error listing log files: {e}")

def demo_pcap_info():
    """Demonstrate PCAP file information"""
    print("\n🔍 PCAP File Information Demo")
    print("=" * 50)
    
    try:
        pcap_files = list_pcap_files(FileConfig.PCAP_FOLDER)
        if pcap_files:
            # Get info for the first PCAP file
            filename = pcap_files[0]['filename']
            print(f"\n📊 Detailed info for: {filename}")
            
            info = get_pcap_file_info(filename, FileConfig.PCAP_FOLDER)
            print(f"  Full path: {info['full_path']}")
            print(f"  Size: {info['size_mb']} MB ({info['size_bytes']} bytes)")
            print(f"  Created: {info['created_time']}")
            print(f"  Modified: {info['modified_time']}")
            print(f"  Accessed: {info['accessed_time']}")
            print(f"  Extension: {info['extension']}")
            print(f"  MIME type: {info['mime_type']}")
            print(f"  Readable: {info['is_readable']}")
        else:
            print("⚠️  No PCAP files found")
    except Exception as e:
        print(f"❌ Error getting PCAP info: {e}")

def demo_log_content():
    """Demonstrate log file content reading"""
    print("\n📖 Log Content Reading Demo")
    print("=" * 50)
    
    try:
        log_files = list_log_files(FileConfig.LOG_FOLDER)
        if log_files:
            # Read content from application.log
            target_file = "application.log"
            print(f"\n📄 Reading content from: {target_file}")
            
            content = get_log_file_content(target_file, FileConfig.LOG_FOLDER, max_lines=10, tail=True)
            print(f"  Total lines: {content['total_lines']}")
            print(f"  Showing: {content['returned_lines']} lines (last 10)")
            print(f"  File size: {content['size_kb']} KB")
            print(f"  Truncated: {content['truncated']}")
            
            print("\n📝 Content preview:")
            lines = content['content'].split('\n')
            for i, line in enumerate(lines[:5], 1):
                if line.strip():
                    print(f"  {i:2d}: {line}")
        else:
            print("⚠️  No log files found")
    except Exception as e:
        print(f"❌ Error reading log content: {e}")

def demo_log_search():
    """Demonstrate log file searching"""
    print("\n🔍 Log Search Demo")
    print("=" * 50)
    
    search_terms = ["ERROR", "WARN", "authentication", "database", "security"]
    
    for term in search_terms:
        print(f"\n🔎 Searching for '{term}':")
        try:
            results = search_log_files(term, FileConfig.LOG_FOLDER, max_results=5)
            print(f"  Found {len(results)} matches")
            
            for result in results[:3]:  # Show first 3 results
                print(f"    📄 {result['filename']}:{result['line_number']}")
                print(f"       {result['line_content'][:80]}...")
        except Exception as e:
            print(f"❌ Error searching for '{term}': {e}")

def demo_agent_queries():
    """Demonstrate example queries for the agent"""
    print("\n🤖 Example Agent Queries")
    print("=" * 50)
    
    example_queries = [
        "List all PCAP files in the pcap folder",
        "Show me the content of the error.log file",
        "Search for 'ERROR' in all log files",
        "Get information about the network_capture_20240115_080005.pcap file",
        "Show me the last 20 lines of the application.log file",
        "Search for 'authentication' in log files",
        "List all log files that contain security information",
        "Find all PCAP files related to network analysis"
    ]
    
    print("\n💡 You can ask the unified agent these types of questions:")
    for i, query in enumerate(example_queries, 1):
        print(f"  {i:2d}. \"{query}\"")
    
    print("\n🔗 Cross-system queries:")
    cross_system_queries = [
        "Find failed test executions from SQL, then search for related errors in log files",
        "Get the commit reference for testcase ID 3, then show me the config file from GitLab and check for related errors in logs",
        "List recent PCAP files and show me test results from the database for the same time period",
        "Find database connection errors in logs and show me the related network traffic from PCAP files"
    ]
    
    for i, query in enumerate(cross_system_queries, 1):
        print(f"  {i:2d}. \"{query}\"")

def demo_file_statistics():
    """Show statistics about the example files"""
    print("\n📊 File Statistics")
    print("=" * 50)
    
    try:
        # PCAP statistics
        pcap_files = list_pcap_files(FileConfig.PCAP_FOLDER)
        total_pcap_size = sum(f['size_bytes'] for f in pcap_files)
        
        print(f"\n📁 PCAP Files Summary:")
        print(f"  Total files: {len(pcap_files)}")
        print(f"  Total size: {total_pcap_size / (1024*1024):.1f} MB")
        print(f"  File types: {', '.join(set(f['extension'] for f in pcap_files))}")
        
        # Log statistics
        log_files = list_log_files(FileConfig.LOG_FOLDER)
        total_log_size = sum(f['size_bytes'] for f in log_files)
        
        print(f"\n📋 Log Files Summary:")
        print(f"  Total files: {len(log_files)}")
        print(f"  Total size: {total_log_size / 1024:.1f} KB")
        print(f"  File types: {', '.join(set(f['extension'] for f in log_files))}")
        
        # Content statistics
        print(f"\n📝 Content Overview:")
        for log_file in log_files:
            try:
                content = get_log_file_content(log_file['filename'], FileConfig.LOG_FOLDER, max_lines=999999)
                print(f"  {log_file['filename']}: {content['total_lines']} lines")
            except:
                print(f"  {log_file['filename']}: Unable to read")
                
    except Exception as e:
        print(f"❌ Error generating statistics: {e}")

def main():
    """Main demo function"""
    print("🎯 File Tools Demo with Example Files")
    print("=" * 60)
    print("This demo shows the file tools functionality using example")
    print("PCAP and log files that have been created for testing.")
    print("=" * 60)
    
    # Run all demos
    demo_list_files()
    demo_pcap_info()
    demo_log_content()
    demo_log_search()
    demo_file_statistics()
    demo_agent_queries()
    
    print("\n" + "=" * 60)
    print("🎉 Demo completed!")
    print("\nTo test with the unified agent, run:")
    print("  python unified_agent_example.py")
    print("\nTo test file tools specifically, run:")
    print("  python test_file_tools.py")
    print("\nTo test all connections, run:")
    print("  python config.py --test-connections")

if __name__ == "__main__":
    main()
