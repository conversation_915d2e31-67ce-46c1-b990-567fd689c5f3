2024-01-15 08:00:00.000 [INFO ] PerformanceMonitor - Performance monitoring started
2024-01-15 08:00:00.100 [INFO ] PerformanceMonitor - Monitoring interval: 30 seconds
2024-01-15 08:00:00.200 [INFO ] PerformanceMonitor - Metrics collection enabled
2024-01-15 08:00:30.300 [INFO ] SystemMetrics - CPU usage: 25.4%
2024-01-15 08:00:30.400 [INFO ] SystemMetrics - Memory usage: 4.2GB / 16GB (26.3%)
2024-01-15 08:00:30.500 [INFO ] SystemMetrics - Disk I/O: Read 45MB/s, Write 23MB/s
2024-01-15 08:00:30.600 [INFO ] SystemMetrics - Network I/O: In 156Mbps, Out 89Mbps
2024-01-15 08:01:00.700 [INFO ] ApplicationMetrics - Active connections: 1,247
2024-01-15 08:01:00.800 [INFO ] ApplicationMetrics - Request queue size: 23
2024-01-15 08:01:00.900 [INFO ] ApplicationMetrics - Average response time: 125ms
2024-01-15 08:01:01.000 [INFO ] ApplicationMetrics - Requests per second: 456
2024-01-15 08:01:30.100 [INFO ] DatabaseMetrics - Connection pool usage: 45/100 (45%)
2024-01-15 08:01:30.200 [INFO ] DatabaseMetrics - Active queries: 12
2024-01-15 08:01:30.300 [INFO ] DatabaseMetrics - Average query time: 45ms
2024-01-15 08:01:30.400 [INFO ] DatabaseMetrics - Slow queries (>1s): 2
2024-01-15 08:02:00.500 [WARN ] SystemMetrics - High CPU usage detected: 78.9%
2024-01-15 08:02:00.600 [WARN ] SystemMetrics - CPU cores: Core 0: 85%, Core 1: 82%, Core 2: 75%, Core 3: 73%
2024-01-15 08:02:00.700 [WARN ] SystemMetrics - Load average: 3.45 (1min), 2.89 (5min), 2.12 (15min)
2024-01-15 08:02:30.800 [INFO ] CacheMetrics - Cache hit ratio: 89.5%
2024-01-15 08:02:30.900 [INFO ] CacheMetrics - Cache size: 2.1GB / 4GB (52.5%)
2024-01-15 08:02:31.000 [INFO ] CacheMetrics - Cache evictions: 145 in last minute
2024-01-15 08:02:31.100 [INFO ] CacheMetrics - Most accessed keys: user_sessions, api_responses
2024-01-15 08:03:00.200 [WARN ] MemoryMetrics - High memory usage: 13.8GB / 16GB (86.3%)
2024-01-15 08:03:00.300 [WARN ] MemoryMetrics - Heap usage: 3.2GB / 4GB (80%)
2024-01-15 08:03:00.400 [WARN ] MemoryMetrics - GC frequency: 15 collections in last minute
2024-01-15 08:03:00.500 [WARN ] MemoryMetrics - GC pause time: Average 45ms, Max 120ms
2024-01-15 08:03:30.600 [ERROR] DiskMetrics - Disk space warning: /var/log 89% full
2024-01-15 08:03:30.700 [ERROR] DiskMetrics - Available space: 2.1GB remaining
2024-01-15 08:03:30.800 [ERROR] DiskMetrics - Disk I/O latency high: 25ms average
2024-01-15 08:03:30.900 [ERROR] DiskMetrics - Disk queue depth: 15 (threshold: 10)
2024-01-15 08:04:00.000 [INFO ] NetworkMetrics - Bandwidth utilization: 67% of 1Gbps
2024-01-15 08:04:00.100 [INFO ] NetworkMetrics - Packet loss: 0.02%
2024-01-15 08:04:00.200 [INFO ] NetworkMetrics - Network latency: 2.5ms average
2024-01-15 08:04:00.300 [INFO ] NetworkMetrics - TCP connections: 2,456 established
2024-01-15 08:04:30.400 [WARN ] ResponseTimeMetrics - Slow endpoint detected
2024-01-15 08:04:30.500 [WARN ] ResponseTimeMetrics - Endpoint: /api/reports/generate
2024-01-15 08:04:30.600 [WARN ] ResponseTimeMetrics - Average response time: 2.8s (threshold: 1s)
2024-01-15 08:04:30.700 [WARN ] ResponseTimeMetrics - 95th percentile: 4.2s
2024-01-15 08:05:00.800 [INFO ] ThroughputMetrics - Peak throughput reached
2024-01-15 08:05:00.900 [INFO ] ThroughputMetrics - Requests per second: 1,247 (peak: 1,500)
2024-01-15 08:05:01.000 [INFO ] ThroughputMetrics - Data throughput: 89MB/s
2024-01-15 08:05:01.100 [INFO ] ThroughputMetrics - Concurrent users: 567
2024-01-15 08:05:30.200 [ERROR] ErrorRateMetrics - High error rate detected
2024-01-15 08:05:30.300 [ERROR] ErrorRateMetrics - Error rate: 5.2% (threshold: 2%)
2024-01-15 08:05:30.400 [ERROR] ErrorRateMetrics - 4xx errors: 3.1%, 5xx errors: 2.1%
2024-01-15 08:05:30.500 [ERROR] ErrorRateMetrics - Most common error: 500 Internal Server Error
2024-01-15 08:06:00.600 [WARN ] LoadBalancerMetrics - Uneven load distribution
2024-01-15 08:06:00.700 [WARN ] LoadBalancerMetrics - Server 1: 45% load, 234ms avg response
2024-01-15 08:06:00.800 [WARN ] LoadBalancerMetrics - Server 2: 35% load, 189ms avg response
2024-01-15 08:06:00.900 [WARN ] LoadBalancerMetrics - Server 3: 20% load, 156ms avg response
2024-01-15 08:06:30.000 [INFO ] SecurityMetrics - Authentication performance
2024-01-15 08:06:30.100 [INFO ] SecurityMetrics - Login requests: 234 per minute
2024-01-15 08:06:30.200 [INFO ] SecurityMetrics - Authentication time: 89ms average
2024-01-15 08:06:30.300 [INFO ] SecurityMetrics - Failed logins: 12 (5.1%)
2024-01-15 08:07:00.400 [WARN ] ThreadPoolMetrics - Thread pool saturation
2024-01-15 08:07:00.500 [WARN ] ThreadPoolMetrics - Active threads: 95/100 (95%)
2024-01-15 08:07:00.600 [WARN ] ThreadPoolMetrics - Queue size: 45 (threshold: 20)
2024-01-15 08:07:00.700 [WARN ] ThreadPoolMetrics - Thread creation rate: 15 per minute
2024-01-15 08:07:30.800 [ERROR] OutOfMemoryDetector - Memory leak suspected
2024-01-15 08:07:30.900 [ERROR] OutOfMemoryDetector - Memory growth rate: 50MB per minute
2024-01-15 08:07:31.000 [ERROR] OutOfMemoryDetector - Heap dump triggered: /tmp/heapdump_20240115_080731.hprof
2024-01-15 08:07:31.100 [ERROR] OutOfMemoryDetector - Investigation required
2024-01-15 08:08:00.200 [INFO ] BackupPerformance - Backup operation metrics
2024-01-15 08:08:00.300 [INFO ] BackupPerformance - Backup size: 15.6GB
2024-01-15 08:08:00.400 [INFO ] BackupPerformance - Backup duration: 23 minutes
2024-01-15 08:08:00.500 [INFO ] BackupPerformance - Backup speed: 11.6MB/s
2024-01-15 08:08:30.600 [WARN ] APIPerformance - API rate limiting triggered
2024-01-15 08:08:30.700 [WARN ] APIPerformance - Client: api_client_12345
2024-01-15 08:08:30.800 [WARN ] APIPerformance - Request rate: 1,200 per minute (limit: 1,000)
2024-01-15 08:08:30.900 [WARN ] APIPerformance - Throttling applied for 5 minutes
2024-01-15 08:09:00.000 [INFO ] ScheduledTaskPerformance - Scheduled task metrics
2024-01-15 08:09:00.100 [INFO ] ScheduledTaskPerformance - Task: daily_report_generation
2024-01-15 08:09:00.200 [INFO ] ScheduledTaskPerformance - Execution time: 45 minutes
2024-01-15 08:09:00.300 [INFO ] ScheduledTaskPerformance - Memory usage: 2.1GB peak
2024-01-15 08:09:30.400 [WARN ] FileSystemPerformance - Slow file operations detected
2024-01-15 08:09:30.500 [WARN ] FileSystemPerformance - File read latency: 45ms (threshold: 10ms)
2024-01-15 08:09:30.600 [WARN ] FileSystemPerformance - File write latency: 67ms (threshold: 20ms)
2024-01-15 08:09:30.700 [WARN ] FileSystemPerformance - Possible disk fragmentation
2024-01-15 08:10:00.800 [INFO ] WebSocketPerformance - WebSocket connection metrics
2024-01-15 08:10:00.900 [INFO ] WebSocketPerformance - Active connections: 1,456
2024-01-15 08:10:01.000 [INFO ] WebSocketPerformance - Message throughput: 2,345 messages/second
2024-01-15 08:10:01.100 [INFO ] WebSocketPerformance - Average message size: 1.2KB
2024-01-15 08:10:30.200 [ERROR] PerformanceAlert - Critical performance threshold exceeded
2024-01-15 08:10:30.300 [ERROR] PerformanceAlert - Metric: Response time
2024-01-15 08:10:30.400 [ERROR] PerformanceAlert - Current value: 3.5s (threshold: 2s)
2024-01-15 08:10:30.500 [ERROR] PerformanceAlert - Alert sent to operations team
