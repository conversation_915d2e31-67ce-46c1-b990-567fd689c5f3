"""
Configuration file for the Unified GitLab & SQL ReAct Agent.

This file contains all the configuration settings for both GitLab and SQL operations.
Modify these settings according to your environment.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class GitLabConfig:
    """GitLab configuration settings"""
    
    # GitLab instance URL
    GITLAB_URL = os.getenv("GITLAB_URL", "https://gitlab.com")
    
    # Project ID (can be found in your GitLab project settings)
    PROJECT_ID = os.getenv("GITLAB_PROJECT_ID", "71217006")
    
    # GitLab access token (create one in GitLab -> User Settings -> Access Tokens)
    TOKEN = os.getenv("GITLAB_TOKEN", "*********************************************************")
    
    @classmethod
    def to_dict(cls):
        """Convert configuration to dictionary format"""
        return {
            "gitlab_url": cls.GITLAB_URL,
            "project_id": cls.PROJECT_ID,
            "token": cls.TOKEN
        }


class SQLConfig:
    """SQL Server configuration settings"""

    # SQL Server connection settings
    SERVER = os.getenv("SQL_SERVER", "localhost")
    DATABASE = os.getenv("SQL_DATABASE", "STAGE")

    # Authentication method
    USE_TRUSTED_CONNECTION = os.getenv("SQL_TRUSTED_CONNECTION", "yes").lower() == "yes"

    # If not using trusted connection, provide username and password
    USERNAME = os.getenv("SQL_USERNAME", "")
    PASSWORD = os.getenv("SQL_PASSWORD", "")

    # Database tables to include in the agent
    TABLES = ["execution", "testcase", "run"]

    # SQL dialect
    DIALECT = "mssql"

    @classmethod
    def get_connection_string(cls):
        """Generate SQL Server connection string"""
        if cls.USE_TRUSTED_CONNECTION:
            return (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={cls.SERVER};"
                f"DATABASE={cls.DATABASE};"
                f"Trusted_Connection=yes;"
            )
        else:
            return (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={cls.SERVER};"
                f"DATABASE={cls.DATABASE};"
                f"UID={cls.USERNAME};"
                f"PWD={cls.PASSWORD};"
            )


class FileConfig:
    """Local file system configuration settings"""

    # PCAP files folder
    PCAP_FOLDER = os.getenv("PCAP_FOLDER", "pcap")

    # Log files folder
    LOG_FOLDER = os.getenv("LOG_FOLDER", "logs")

    # File patterns
    PCAP_PATTERNS = ["*.pcap", "*.pcapng", "*.cap"]
    LOG_PATTERNS = ["*.log", "*.txt", "*.out", "*.err"]

    # Default limits
    MAX_LOG_LINES = int(os.getenv("MAX_LOG_LINES", "1000"))
    MAX_SEARCH_RESULTS = int(os.getenv("MAX_SEARCH_RESULTS", "100"))

    @classmethod
    def to_dict(cls):
        """Convert configuration to dictionary format"""
        return {
            "pcap_folder": cls.PCAP_FOLDER,
            "log_folder": cls.LOG_FOLDER
        }
    
    # Database tables to include in the agent
    TABLES = ["execution", "testcase", "run"]
    
    # SQL dialect
    DIALECT = "mssql"
    
    @classmethod
    def get_connection_string(cls):
        """Generate SQL Server connection string"""
        if cls.USE_TRUSTED_CONNECTION:
            return (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={cls.SERVER};"
                f"DATABASE={cls.DATABASE};"
                f"Trusted_Connection=yes;"
            )
        else:
            return (
                f"DRIVER={{ODBC Driver 17 for SQL Server}};"
                f"SERVER={cls.SERVER};"
                f"DATABASE={cls.DATABASE};"
                f"UID={cls.USERNAME};"
                f"PWD={cls.PASSWORD};"
            )


class MistralConfig:
    """Mistral AI configuration settings"""
    
    # Mistral API key (get from https://console.mistral.ai/)
    API_KEY = os.getenv("MISTRAL_API_KEY", "dMQ92i6Fl14LLU2r1UmIJf9M8JHAzgRf")
    
    # Model selection
    MODEL = os.getenv("MISTRAL_MODEL", "mistral-large-latest")
    
    # Alternative model for GitLab operations (lighter/faster)
    GITLAB_MODEL = os.getenv("MISTRAL_GITLAB_MODEL", "mistral-medium")
    
    # API endpoint
    API_URL = "https://api.mistral.ai/v1/chat/completions"
    
    # Model parameters
    TEMPERATURE = 0.0
    MAX_TOKENS = 1000


class AgentConfig:
    """Agent behavior configuration"""
    
    # Maximum number of iterations for the agent
    MAX_ITERATIONS = 15
    
    # Execution timeout in seconds
    EXECUTION_TIMEOUT = 300.0
    
    # Enable/disable SQL functionality
    ENABLE_SQL = os.getenv("ENABLE_SQL", "true").lower() == "true"
    
    # Enable/disable verbose logging
    VERBOSE = os.getenv("AGENT_VERBOSE", "true").lower() == "true"
    
    # Enable/disable debug mode
    DEBUG = os.getenv("AGENT_DEBUG", "false").lower() == "true"


class LoggingConfig:
    """Logging configuration"""
    
    # Log level
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    
    # Log format
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Log file path (optional)
    LOG_FILE = os.getenv("LOG_FILE", None)


def validate_config():
    """Validate configuration settings"""
    errors = []
    
    # Validate GitLab configuration
    if not GitLabConfig.TOKEN:
        errors.append("GitLab token is required")
    
    if not GitLabConfig.PROJECT_ID:
        errors.append("GitLab project ID is required")
    
    # Validate Mistral configuration
    if not MistralConfig.API_KEY:
        errors.append("Mistral API key is required")
    
    # Validate SQL configuration (if enabled)
    if AgentConfig.ENABLE_SQL:
        if not SQLConfig.USE_TRUSTED_CONNECTION:
            if not SQLConfig.USERNAME or not SQLConfig.PASSWORD:
                errors.append("SQL username and password are required when not using trusted connection")
    
    if errors:
        raise ValueError("Configuration errors:\n" + "\n".join(f"- {error}" for error in errors))
    
    return True


def print_config_summary():
    """Print a summary of current configuration"""
    print("🔧 Configuration Summary")
    print("=" * 40)
    
    print(f"GitLab URL: {GitLabConfig.GITLAB_URL}")
    print(f"GitLab Project ID: {GitLabConfig.PROJECT_ID}")
    print(f"GitLab Token: {'*' * 20}...{GitLabConfig.TOKEN[-4:] if GitLabConfig.TOKEN else 'Not set'}")
    
    print(f"\nMistral Model: {MistralConfig.MODEL}")
    print(f"Mistral API Key: {'*' * 20}...{MistralConfig.API_KEY[-4:] if MistralConfig.API_KEY else 'Not set'}")
    
    if AgentConfig.ENABLE_SQL:
        print(f"\nSQL Server: {SQLConfig.SERVER}")
        print(f"SQL Database: {SQLConfig.DATABASE}")
        print(f"SQL Auth: {'Trusted Connection' if SQLConfig.USE_TRUSTED_CONNECTION else 'Username/Password'}")
        print(f"SQL Tables: {', '.join(SQLConfig.TABLES)}")
    else:
        print("\nSQL: Disabled")
    
    print(f"\nAgent Max Iterations: {AgentConfig.MAX_ITERATIONS}")
    print(f"Agent Timeout: {AgentConfig.EXECUTION_TIMEOUT}s")
    print(f"Verbose Logging: {AgentConfig.VERBOSE}")
    print("=" * 40)


# Example .env file content
ENV_TEMPLATE = """
# GitLab Configuration
GITLAB_URL=https://gitlab.com
GITLAB_PROJECT_ID=your_project_id
GITLAB_TOKEN=your_gitlab_token

# Mistral AI Configuration
MISTRAL_API_KEY=your_mistral_api_key
MISTRAL_MODEL=mistral-large-latest

# SQL Server Configuration
SQL_SERVER=localhost
SQL_DATABASE=STAGE
SQL_TRUSTED_CONNECTION=yes
# SQL_USERNAME=your_username
# SQL_PASSWORD=your_password

# File System Configuration
PCAP_FOLDER=pcap
LOG_FOLDER=log
MAX_LOG_LINES=1000
MAX_SEARCH_RESULTS=100

# Agent Configuration
ENABLE_SQL=true
AGENT_VERBOSE=true
AGENT_DEBUG=false

# Logging Configuration
LOG_LEVEL=INFO
# LOG_FILE=agent.log
"""

def test_database_connection():
    """Test the database connection"""
    print("🔌 Testing Database Connection...")
    print("-" * 40)

    try:
        import pyodbc
        import urllib.parse
        from sqlalchemy import create_engine, text

        # Get connection string
        connection_string = SQLConfig.get_connection_string()
        print(f"Connection String: {connection_string}")

        # Test with pyodbc directly
        print("\n1. Testing with pyodbc...")
        try:
            conn = pyodbc.connect(connection_string)
            cursor = conn.cursor()
            cursor.execute("SELECT @@VERSION")
            version = cursor.fetchone()[0]
            print(f"✅ pyodbc connection successful!")
            print(f"   SQL Server Version: {version[:50]}...")
            cursor.close()
            conn.close()
        except Exception as e:
            print(f"❌ pyodbc connection failed: {e}")
            return False

        # Test with SQLAlchemy
        print("\n2. Testing with SQLAlchemy...")
        try:
            params = urllib.parse.quote_plus(connection_string)
            engine = create_engine(f"mssql+pyodbc:///?odbc_connect={params}")

            with engine.connect() as conn:
                result = conn.execute(text("SELECT DB_NAME() as current_database"))
                db_name = result.fetchone()[0]
                print(f"✅ SQLAlchemy connection successful!")
                print(f"   Current Database: {db_name}")
        except Exception as e:
            print(f"❌ SQLAlchemy connection failed: {e}")
            return False

        # Test table access
        print("\n3. Testing table access...")
        try:
            with engine.connect() as conn:
                for table in SQLConfig.TABLES:
                    try:
                        result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        count = result.fetchone()[0]
                        print(f"✅ Table '{table}': {count} rows")
                    except Exception as e:
                        print(f"❌ Table '{table}': {e}")
        except Exception as e:
            print(f"❌ Table access test failed: {e}")
            return False

        print("\n🎉 All database tests passed!")
        return True

    except ImportError as e:
        print(f"❌ Missing required packages: {e}")
        print("Please install: pip install pyodbc sqlalchemy")
        return False
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


def test_gitlab_connection():
    """Test the GitLab connection"""
    print("🦊 Testing GitLab Connection...")
    print("-" * 40)

    try:
        import requests

        # Test GitLab API access
        url = f"{GitLabConfig.GITLAB_URL}/api/v4/projects/{GitLabConfig.PROJECT_ID}"
        headers = {"PRIVATE-TOKEN": GitLabConfig.TOKEN}

        print(f"Testing: {url}")

        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code == 200:
            project_data = response.json()
            print(f"✅ GitLab connection successful!")
            print(f"   Project: {project_data.get('name', 'Unknown')}")
            print(f"   Namespace: {project_data.get('namespace', {}).get('full_path', 'Unknown')}")
            print(f"   Default Branch: {project_data.get('default_branch', 'Unknown')}")
            return True
        else:
            print(f"❌ GitLab API error: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False

    except ImportError:
        print("❌ Missing 'requests' package. Install with: pip install requests")
        return False
    except Exception as e:
        print(f"❌ GitLab connection failed: {e}")
        return False


def test_mistral_connection():
    """Test the Mistral AI connection"""
    print("🧠 Testing Mistral AI Connection...")
    print("-" * 40)

    try:
        import requests

        # Test Mistral API access
        headers = {
            "Authorization": f"Bearer {MistralConfig.API_KEY}",
            "Content-Type": "application/json"
        }

        # Simple test payload
        payload = {
            "model": MistralConfig.MODEL,
            "messages": [{"role": "user", "content": "Hello, this is a connection test."}],
            "max_tokens": 10
        }

        print(f"Testing: {MistralConfig.API_URL}")
        print(f"Model: {MistralConfig.MODEL}")

        response = requests.post(MistralConfig.API_URL, headers=headers, json=payload, timeout=30)

        if response.status_code == 200:
            print("✅ Mistral AI connection successful!")
            return True
        else:
            print(f"❌ Mistral API error: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False

    except ImportError:
        print("❌ Missing 'requests' package. Install with: pip install requests")
        return False
    except Exception as e:
        print(f"❌ Mistral connection failed: {e}")
        return False


def run_connection_tests():
    """Run all connection tests"""
    print("🧪 Running Connection Tests")
    print("=" * 50)

    tests = []

    # Test GitLab
    gitlab_success = test_gitlab_connection()
    tests.append(("GitLab", gitlab_success))

    # Test Mistral
    mistral_success = test_mistral_connection()
    tests.append(("Mistral AI", mistral_success))

    # Test Database (if enabled)
    if AgentConfig.ENABLE_SQL:
        db_success = test_database_connection()
        tests.append(("SQL Database", db_success))
    else:
        print("\n🗄️  SQL Database testing skipped (disabled in config)")

    # Test File System
    fs_success = test_file_system()
    tests.append(("File System", fs_success))

    # Summary
    print("\n" + "=" * 50)
    print("📊 Connection Test Summary")
    print("=" * 50)

    passed = 0
    for service, success in tests:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{service}: {status}")
        if success:
            passed += 1

    total = len(tests)
    print(f"\nOverall: {passed}/{total} connections successful")

    if passed == total:
        print("🎉 All connections working! You're ready to use the agent.")
    else:
        print("⚠️  Some connections failed. Please check your configuration.")

    return passed == total


def test_file_system():
    """Test the file system access for PCAP and log folders"""
    print("📁 Testing File System Access...")
    print("-" * 40)

    success = True

    # Test PCAP folder
    print(f"1. Testing PCAP folder: {FileConfig.PCAP_FOLDER}")
    try:
        if not os.path.exists(FileConfig.PCAP_FOLDER):
            print(f"⚠️  PCAP folder '{FileConfig.PCAP_FOLDER}' does not exist")
            print(f"   Creating folder...")
            os.makedirs(FileConfig.PCAP_FOLDER, exist_ok=True)
            print(f"✅ PCAP folder created")
        else:
            print(f"✅ PCAP folder exists")

        # Check if it's a directory
        if not os.path.isdir(FileConfig.PCAP_FOLDER):
            print(f"❌ '{FileConfig.PCAP_FOLDER}' is not a directory")
            success = False
        else:
            # Check permissions
            if os.access(FileConfig.PCAP_FOLDER, os.R_OK):
                print(f"✅ PCAP folder is readable")
            else:
                print(f"❌ No read permission for PCAP folder")
                success = False

            # List files
            import glob
            pcap_files = glob.glob(os.path.join(FileConfig.PCAP_FOLDER, "*.pcap"))
            pcap_files.extend(glob.glob(os.path.join(FileConfig.PCAP_FOLDER, "*.pcapng")))
            print(f"   Found {len(pcap_files)} PCAP files")

    except Exception as e:
        print(f"❌ PCAP folder test failed: {e}")
        success = False

    # Test Log folder
    print(f"\n2. Testing Log folder: {FileConfig.LOG_FOLDER}")
    try:
        if not os.path.exists(FileConfig.LOG_FOLDER):
            print(f"⚠️  Log folder '{FileConfig.LOG_FOLDER}' does not exist")
            print(f"   Creating folder...")
            os.makedirs(FileConfig.LOG_FOLDER, exist_ok=True)
            print(f"✅ Log folder created")
        else:
            print(f"✅ Log folder exists")

        # Check if it's a directory
        if not os.path.isdir(FileConfig.LOG_FOLDER):
            print(f"❌ '{FileConfig.LOG_FOLDER}' is not a directory")
            success = False
        else:
            # Check permissions
            if os.access(FileConfig.LOG_FOLDER, os.R_OK):
                print(f"✅ Log folder is readable")
            else:
                print(f"❌ No read permission for log folder")
                success = False

            # List files
            import glob
            log_files = glob.glob(os.path.join(FileConfig.LOG_FOLDER, "*.log"))
            log_files.extend(glob.glob(os.path.join(FileConfig.LOG_FOLDER, "*.txt")))
            print(f"   Found {len(log_files)} log files")

    except Exception as e:
        print(f"❌ Log folder test failed: {e}")
        success = False

    # Test file tools
    print(f"\n3. Testing file tools...")
    try:
        from file_tools import list_pcap_files, list_log_files

        pcap_list = list_pcap_files(FileConfig.PCAP_FOLDER)
        log_list = list_log_files(FileConfig.LOG_FOLDER)

        print(f"✅ File tools working")
        print(f"   PCAP files found: {len(pcap_list)}")
        print(f"   Log files found: {len(log_list)}")

    except Exception as e:
        print(f"❌ File tools test failed: {e}")
        success = False

    if success:
        print("\n🎉 All file system tests passed!")
    else:
        print("\n⚠️  Some file system tests failed. Check folder permissions and paths.")

    return success


def create_env_template():
    """Create a template .env file"""
    if not os.path.exists(".env"):
        with open(".env.template", "w") as f:
            f.write(ENV_TEMPLATE.strip())
        print("📝 Created .env.template file. Copy it to .env and update with your settings.")
    else:
        print("📝 .env file already exists.")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--test-connections":
        # Run connection tests
        run_connection_tests()
    elif len(sys.argv) > 1 and sys.argv[1] == "--test-db":
        # Test only database connection
        test_database_connection()
    elif len(sys.argv) > 1 and sys.argv[1] == "--test-gitlab":
        # Test only GitLab connection
        test_gitlab_connection()
    elif len(sys.argv) > 1 and sys.argv[1] == "--test-mistral":
        # Test only Mistral connection
        test_mistral_connection()
    elif len(sys.argv) > 1 and sys.argv[1] == "--test-files":
        # Test only file system
        test_file_system()
    else:
        # Default behavior: validate and print configuration
        try:
            validate_config()
            print_config_summary()
            create_env_template()
            print("\n💡 To test connections, run:")
            print("   python config.py --test-connections    # Test all connections")
            print("   python config.py --test-db            # Test database only")
            print("   python config.py --test-gitlab        # Test GitLab only")
            print("   python config.py --test-mistral       # Test Mistral AI only")
            print("   python config.py --test-files         # Test file system only")
        except ValueError as e:
            print(f"❌ {e}")
